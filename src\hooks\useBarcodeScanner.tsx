"use client"

import { TProduct } from "@/app/(main)/dashboard/misc/types"
import { useEffect, useState } from "react"
import toast from "react-hot-toast"

interface UseBarcodeScannerProps {
  products: TProduct[] | undefined
  onProductFound: (product: TProduct) => void
}

export const useBarcodeScanner = ({ products, onProductFound }: UseBarcodeScannerProps) => {
  const [barcodeBuffer, setBarcodeBuffer] = useState<string>("")
  const [lastScanTime, setLastScanTime] = useState<number>(0)

  useEffect(() => {
    if (!products) return

    const handleKeyPress = (event: KeyboardEvent) => {
      const currentTime = new Date().getTime()

      if (event.key === "Enter" && barcodeBuffer) {
        event.preventDefault()

        const foundProduct = products.find((product) => product.sku === barcodeBuffer)

        if (foundProduct) {
          onProductFound(foundProduct)
          toast(`${foundProduct.item} has been added to cart`)
        } else {
          toast.error("No product found with this barcode")
        }

        setBarcodeBuffer("")
        return
      }

      if (currentTime - lastScanTime > 500) {
        setBarcodeBuffer("")
      }

      if (/^[a-zA-Z0-9]$/.test(event.key)) {
        setBarcodeBuffer((prev) => prev + event.key)
        setLastScanTime(currentTime)
      }
    }

    window.addEventListener("keydown", handleKeyPress)

    return () => {
      window.removeEventListener("keydown", handleKeyPress)
    }
  }, [barcodeBuffer, lastScanTime, onProductFound, products,])
}
