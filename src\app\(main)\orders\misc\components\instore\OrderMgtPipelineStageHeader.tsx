// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ubar<PERSON><PERSON>nt, <PERSON>ubar<PERSON><PERSON>, MenubarMenu, MenubarSeparator, MenubarSub, <PERSON>ubarSub<PERSON><PERSON>nt, <PERSON>ubarSub<PERSON>rig<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lT<PERSON> } from '@/components/core'
import {
  ToolTip,
} from "@/components/ui";
import { Search } from "lucide-react";
// import { Elipsis, MagnifyingLens } from '@/icons/core'
// import FilterIcon from '@/icons/core/FilterIcon'
import React from "react";

interface OrderMgtPipelineStageHeaderProps {
  stage: string;
  openSearchBox: () => void;
  openPreviewModal: () => void;
  searchText: string;
  openConfirmProgressModal: () => void;
  itemCount: number;
}
const OrderMgtPipelineStageHeader: React.FC<
  OrderMgtPipelineStageHeaderProps
> = ({
  stage,
  openSearchBox,
  searchText,
  itemCount,
}) => {
  return (
    <header className="sticky top-0 flex items-center justify-between gap-4 px-4 py-2 bg-black rounded-t-xl text-primary font-medium w-full">
      <section className="flex items-center gap-4 text-sm">
        <h2 className="flex items-center gap-1.5 text-sm truncate">
          {stage ?? ""}
          <span className="flex items-center justify-center px-2 py-1 bg-[#1C1C1C] rounded-md text-xs font-bold">
            {itemCount ?? 0}
          </span>
        </h2>
        {/* <Input type="text" className="!h-8" /> */}
      </section>

      <section className="flex items-center gap-3">
        {/* <ToolTip content="Filter applicants" contentClass="text-xs font-normal">
          <div
            className="relative flex items-center justify-center w-7 h-7 rounded-full bg-[#272727] "
            onClick={openPreviewModal}
          >
            <FilterIcon size={15} />
            {filterUrl && filterUrl.trim() !== "" && (
              <span className="absolute h-[0.45rem] w-[0.45rem] rounded-full -top-0 -right-0 bg-danger"></span>
            )}
          </div>
        </ToolTip> */}

        <ToolTip content="Search stage" contentClass="text-xs font-normal">
          <div
            className="relative flex items-center justify-center w-7 h-7 rounded-full bg-[#272727] "
            onClick={openSearchBox}
          >
            <Search size={15} />
            {searchText && searchText.trim() !== "" && (
              <span className="absolute h-[0.45rem] w-[0.45rem] rounded-full -top-0 -right-0 bg-danger"></span>
            )}
          </div>
        </ToolTip>

        {/* <Menubar className="ml-auto border-0">
          <MenubarMenu>
            <MenubarTrigger className="flex items-center justify-center !ml-auto p-0 h-7 w-7 rounded-full bg-[#272727] border-none cursor-pointer">
              <ToolTip
                content="More"
                contentClass="text-xs font-normal"
                asChild
              >
                <EllipsisVertical size={15} color="white" />
              </ToolTip>
            </MenubarTrigger>

            <MenubarContent align="end">
              <MenubarItem>Edit stage</MenubarItem>
              <MenubarItem>Export stage data</MenubarItem>

              
            </MenubarContent>
          </MenubarMenu>
        </Menubar> */}
      </section>
    </header>
  );
};

export default OrderMgtPipelineStageHeader;
