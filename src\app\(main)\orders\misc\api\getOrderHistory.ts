import { managementAxios } from '@/lib/axios';
import { useQuery } from '@tanstack/react-query';
import { TOrderMgtBranchOrderHistory } from '../types';



interface fetchBranchOrdesHistoryResponse {
  count: number,
  next: string | null,
  previous: string | null,
  results: TOrderMgtBranchOrderHistory[]
}

interface FetchOrderHistoryOptions {
  branchId: string;
  pageIndex: number;
  pageSize: number;
  startDate?: string | undefined;
  endDate?: string | undefined;
  search?: string;
}

export const getOrdersHistory = async ({
  branchId,
  pageIndex,
  pageSize,
  // startDate,
  // endDate,
  search,
}: FetchOrderHistoryOptions) => {
  // }: FetchOrderHistoryOptions): Promise<TeamsResponse> => {
  const response = await managementAxios.get(
    `/orders/branch/${branchId}/history?page=${pageIndex}&size=${pageSize}&search=${search}`

    // `/req/get-teams/?company=${companyId}&page=${pageIndex}&size=${pageSize}&timestamp_gte=${startDate}&timestamp_lte=${endDate}&search=${search}`
  );

  return response.data as fetchBranchOrdesHistoryResponse
};

export const useGetOrderHistory = (fetchOptions: FetchOrderHistoryOptions) => {
  const { search, startDate, endDate, pageIndex, pageSize, branchId } = fetchOptions
  return useQuery({
    queryKey: [`branch-${branchId}-orders-history`, fetchOptions, search, startDate, endDate, pageIndex, pageSize],
    queryFn: () => {
      if (fetchOptions) return getOrdersHistory(fetchOptions);
    },
    enabled: !!fetchOptions,
    
  });
};
