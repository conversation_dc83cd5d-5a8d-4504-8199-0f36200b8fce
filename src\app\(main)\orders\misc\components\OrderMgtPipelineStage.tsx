
import React, { Dispatch, SetStateAction, useRef, useState } from 'react';
import { Droppable } from '@hello-pangea/dnd';

import { ConfirmActionModal, ConfirmDeleteModal } from '@/components/ui';

import OrderMgtPipelineStageHeader from './OrderMgtPipelineStageHeader';
import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from '../types';
import OrderMgtPipelineStageOrderCard from './OrderMgtPipelineStageOrderCard';
import { useBooleanStateControl } from '@/hooks';
import { UseCancelOrder, UseMoveOrdertoNextStage } from '../api';
import toast from 'react-hot-toast';

interface OrderMgtPipelineStageProps {
    branch_id: string
    pipeline_id: string
    data: OrderMgtPipelineStageDetails
    filterUrl: string
    setFilterUrl: Dispatch<SetStateAction<string>>
    allStages: OrderMgtPipelineStageDetails[]
    currentStage: OrderMgtPipelineStageDetails
    refetchPipelineData: () => void
}


const OrderMgtPipelineStage: React.FC<OrderMgtPipelineStageProps> = ({ data, filterUrl, branch_id, pipeline_id, allStages, refetchPipelineData,
    currentStage,
}) => {

    const {
        state: isSearchBoxOpen,
        setFalse: closeSearchBox,
        toggle: toggleSearchBox
    } = useBooleanStateControl(false)
    // useEffect(() => {
    //     setFilterUrl(filterUrl)
    // }, [filterUrl])
    const orders = currentStage.data

    const searchTextBoxRef = useRef<HTMLInputElement | null>(null);
    const [searchText, setSearchText] = React.useState('')
    const [currentStageOrders, setCurrentStageOrders] = useState<TOrderMgtPipelineStageOrder[]>(orders.filter((order) => order.current_stage.name === currentStage?.stage_name));
    const [ordersToDisplay, setOrdersToDisplay] = useState<TOrderMgtPipelineStageOrder[]>(currentStageOrders || orders.filter((order) => order.current_stage.name === currentStage?.stage_name))
    const [selectedOrders, setSelectedOrders] = React.useState<string[]>([])
    // const currentStage = allStages.find((stage) => stage.stage_name === column) || {} as OrderMgtPipelineStageDetails
    const nextStageId = allStages[(currentStage?.position || 0)]?.stage_id




    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////                          BOOLEAN STATES                     //////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    const {
        state: isConfirmProgressSelectedOrdersModalOpen,
        setTrue: openConfirmProgressSelectedOrdersModal,
        setFalse: closeConfirmProgressSelectedOrdersModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmCancelSelectedOrdersModalOpen,
        setTrue: openConfirmCancelSelectedOrdersModal,
        setFalse: closeConfirmCancelSelectedOrdersModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmCancelRangeOrdersModalOpen,
        setTrue: openConfirmCancelRangeOrdersModal,
        setFalse: closeConfirmCancelRangeOrderRangeModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmProgressRangeOrdersModalOpen,
        setTrue: openConfirmProgressRangeOrdersModal,
        setFalse: closeConfirmProgressRangeOrderal,
    } = useBooleanStateControl(false)

    React.useEffect(() => {
        setCurrentStageOrders(orders.filter((order) => order.current_stage.name === currentStage?.stage_name))
    }, [orders])

    React.useEffect(() => {
        if (isSearchBoxOpen && searchTextBoxRef.current) {
            searchTextBoxRef.current.focus();
        }
    }, [isSearchBoxOpen]);

    React.useEffect(() => {
        if (searchText && searchText.trim() !== "") {
            setOrdersToDisplay(orders.filter((order) =>
                order.current_stage.name === currentStage?.stage_name && (
                    order.contact_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.contact_phone_number?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.first_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.last_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.email?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.order_products.some((product) => product.product_name.toLowerCase().includes(searchText.toLowerCase())) ||
                    order.channels?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.status?.toLowerCase().includes(searchText) ||
                    order.buyer.address?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.city?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.state?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.country?.toLowerCase().includes(searchText.toLowerCase())
                )
            ))

        } else {
            setOrdersToDisplay(orders.filter((order) => order.current_stage.name === currentStage?.stage_name));
        }
    }, [searchText, orders, currentStage?.stage_name]);


    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////                          ORDER ACTIONS                      //////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    const [startProgressIndex, setStartProgressIndex] = useState(0)
    const [endProgressIndex, setEndProgressIndex] = useState(0)
    const { mutate: moveOrderToNextStage, isPending: isMovingOders } = UseMoveOrdertoNextStage()

    const { mutate: moveOrderToNewStage, } = UseMoveOrdertoNextStage()
    const { mutate: cancelTheOrder, isPending: isCancellingOrder } = UseCancelOrder()


    const progressSelectedOrders = () => {
        if (nextStageId) {
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: selectedOrders,
                pipeline_id: pipeline_id

            }, {
                onSuccess() {
                    toast.success('Orders moved successfully');
                    refetchPipelineData()
                    closeConfirmProgressSelectedOrdersModal()
                }
            })
        }
    }
    const cancelSelectedOrders = () => {
        cancelTheOrder({
            order_ids: selectedOrders,
        }, {
            onSuccess() {
                toast.success('Orders cancelled successfully');
                refetchPipelineData()
                closeConfirmCancelSelectedOrdersModal()
            }
        })
    }
    const cancelOrderRange = () => {
        if (startProgressIndex === 0 || endProgressIndex === 0) return
        if (startProgressIndex > endProgressIndex) return toast.error('Invalid range selected, start index cannot be greater than end index')
        if (endProgressIndex > currentStageOrders.length) return toast.error(`Invalid range selected, current stage has only ${currentStageOrders.length} orders`)
        const ordersToCancel = currentStageOrders.slice(startProgressIndex - 1, endProgressIndex)
        const orderIds = ordersToCancel.map((order) => order.id)
        cancelTheOrder({
            order_ids: orderIds,
        }, {
            onSuccess() {
                toast.success('Orders cancelled successfully');
                refetchPipelineData()
                closeConfirmCancelSelectedOrdersModal()
            }
        })
    }
    const progressOrderRange = () => {
        if (startProgressIndex === 0 || endProgressIndex === 0) return
        if (startProgressIndex > endProgressIndex) return toast.error('Invalid range selected, start index cannot be greater than end index')
        if (endProgressIndex > currentStageOrders.length) return toast.error(`Invalid range selected, current stage has only ${currentStageOrders.length} orders`)
        if (nextStageId) {
            const ordersToProgress = currentStageOrders.slice(startProgressIndex - 1, endProgressIndex)
            const orderIds = ordersToProgress.map((order) => order.id)
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: orderIds,
                pipeline_id: pipeline_id
            }, {
                onSuccess() {
                    toast.success('Orders moved successfully');
                    refetchPipelineData()
                    closeConfirmProgressRangeOrderal()
                }
            })
        }
    }
    return (
        <Droppable droppableId={data.stage_id}>
            {(provided) => (
                <article className='flex flex-col !shrink-0  gap-2 mt-0 pt-1 px-2 h-full overflow-y-scroll max-md:min-w-[90vw]  w-max min-w-[200px] lg:w-[400px] max-w-[425px] '>
                    {/* <a className='hidden' href="" ref={xlsxRef}></a> */}
                    <OrderMgtPipelineStageHeader
                        endProgressIndex={endProgressIndex}
                        filterUrl={""}
                        openConfirmCancelRangeOrdersModal={openConfirmCancelRangeOrdersModal}
                        openConfirmCancelSelectedOrdersModal={openConfirmCancelSelectedOrdersModal}
                        openConfirmProgressRangeOrdersModal={openConfirmProgressRangeOrdersModal}
                        openConfirmProgressSelectedOrdersModal={openConfirmProgressSelectedOrdersModal}
                        openSearchBox={toggleSearchBox}
                        pipeline_id={pipeline_id}
                        searchText={searchText}
                        selectedOrders={selectedOrders}
                        setEndProgressIndex={setEndProgressIndex}
                        setStartProgressIndex={setStartProgressIndex}
                        stage={currentStage || {} as OrderMgtPipelineStageDetails}
                        startProgressIndex={startProgressIndex}
                    />


                    {/* {
                        isSearchBoxOpen &&
                        <div className='relative grid grid-cols-[1fr,0.2fr,max-content] max-w-full items-center justify-between gap-2 border-[1.6px] border-[#D6D6D6] p-4 py-1 rounded-lg focus-within:border-primary'>
                            <input
                                className=' outline-none  text-sm py-1'
                                placeholder='start typing'
                                ref={searchTextBoxRef}
                                type="text"
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                            />
                            <Select
                                className='!p-1 !text-xs max-md:max-w-[6rem] md:max-lg:max-w-[5rem] lg:max-w-[7rem]'
                                containerClass='!my-0'
                                errors={{ null: { message: "" } }}
                                itemClass='!text-xs py-1'
                                name='search_by'
                                options={searchFilters}
                                placeholder='Search by'
                                value={searchBy}
                                onChange={(e) => setSearchBy(e)}
                            />
                            <Button className='w-6 h-6 bg-[#F9FAFB] hover:bg-red-300 !rounded-full' size='icon' variant='unstyled' onClick={() => { closeSearchBox(); setSearchText("") }}>
                                <XIcon />
                            </Button>
                        </div>
                    } */}




                    <section {...provided.droppableProps} className='flex flex-col grow overflow-y-scroll p-4 bg-black rounded-b-xl' ref={provided.innerRef}>
                        {
                            ordersToDisplay?.map((order, index) => (
                                <OrderMgtPipelineStageOrderCard
                                    allOrdersData={orders}
                                    allOrdersId={orders.map((order) => order.id)}
                                    branchId={branch_id}
                                    currentStage={currentStage}
                                    key={order.id}
                                    index={index}
                                    nextStageId={nextStageId}
                                    order={order}
                                    pipelineId={pipeline_id}
                                    refetchPipelineData={refetchPipelineData}
                                    selectedOrders={selectedOrders}
                                    setSelectedOrders={setSelectedOrders}
                                />
                            ))
                        }
                        {provided.placeholder}
                    </section>






                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* //////////////                           MODALS                           //////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}


                    <ConfirmActionModal
                        closeModal={closeConfirmProgressRangeOrderal}
                        confirmFunction={progressOrderRange}
                        isConfirmingAction={isMovingOders}
                        isModalOpen={isConfirmProgressRangeOrdersModalOpen}
                        title="Progress candidates to next stage"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to progress  orders no:- <span className='text-header-text font-bold ml-1'>{startProgressIndex}</span> - <span className='text-header-text font-bold mr-1'>{endProgressIndex}</span> to the next stage,
                        </p>
                    </ConfirmActionModal>

                    <ConfirmActionModal
                        closeModal={closeConfirmProgressSelectedOrdersModal}
                        confirmFunction={progressSelectedOrders}
                        isConfirmingAction={isMovingOders}
                        isModalOpen={isConfirmProgressSelectedOrdersModalOpen}
                        title="Progress candidates to next stage"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to progress  <span className='text-header-text font-bold mr-1'>{selectedOrders.length}</span>
                            {selectedOrders.length < 2 ? "order" : "orders"} to the next stage,
                            {/* Please be aware that any candidates progressed to a stage with email notification enabled will <span className='text-header-text font-medium mr-1'>receive an email</span> from us even if you move them back later. */}
                        </p>
                    </ConfirmActionModal>
                    <ConfirmActionModal
                        closeModal={closeConfirmCancelSelectedOrdersModal}
                        confirmFunction={cancelSelectedOrders}
                        isConfirmingAction={isCancellingOrder}
                        isModalOpen={isConfirmCancelSelectedOrdersModalOpen}
                        title="Cancel candidates"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to cancel <span className='text-header-text font-bold mr-1'>{selectedOrders.length}</span>
                            {selectedOrders.length < 2 ? "order" : "orders"}.
                        </p>
                    </ConfirmActionModal>

                    <ConfirmActionModal
                        closeModal={closeConfirmCancelRangeOrderRangeModal}
                        confirmFunction={cancelOrderRange}
                        isConfirmingAction={isCancellingOrder}
                        isModalOpen={isConfirmCancelRangeOrdersModalOpen}
                        title="Cancel candidates"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to cancel orders no:- <span className='text-header-text font-bold ml-1'>{startProgressIndex}</span> - <span className='text-header-text font-bold mr-1'>{endProgressIndex}</span>.
                        </p>
                    </ConfirmActionModal>
                </article>
            )}
        </Droppable>
    );
};

export default OrderMgtPipelineStage;