import {
  ISalesTransactionDto,
  SalesTransactionDto,
  SingleSaleHistory,
  useGetPendingOrders,
} from "@/app/(main)/dashboard/misc/api/getPendingOrders";
import {
  Button,
  Input,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui";
import { addCommasToNumber } from "@/utils/numbers";
import { format } from "date-fns";
import { CountIcon, MoneyIcon, SavedSalesIcon } from "../icons";
import { useBooleanStateControl } from "@/hooks";
import React from "react";
import SingleTransaction from "./SingleTransaction";
import { SmallSpinner } from "@/components/icons";

interface PendingOrdersTableProps {
  branch: string;
  company: string;
}

const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const SavedSalesTable = ({ branch, company }: PendingOrdersTableProps) => {
  const fetchOptions = {
    companyId: company,
    branchId: branch,
    // pageIndex,
    // pageSize,
    // startDate,
    // endDate,
    // search: debouncedSearchFilter,
  };
  const {
    data: pendingOrdersResponse,
    isLoading,
    isFetching,
  } = useGetPendingOrders(fetchOptions);
  const {
    sales_transactions,
    count: numberOfPendingOrders,
    total_sales,
  } = pendingOrdersResponse?.data || {};
  const [singleInvoice, setSingleInvoice] =
    React.useState<ISalesTransactionDto>();

  const {
    state: isSingleTxOpen,
    setTrue: openSingleTxOpen,
    setFalse: closeSingleTxOpen,
  } = useBooleanStateControl();

  return (
    <>
      <>
        <div className="sticky top-0 flex items-center justify-between w-full pl-4 py-4 bg-[#1C1C1C] z-[2]">
          <h2 className="font-medium text-xl">Total Saved Sales</h2>
          <Input
            placeholder="Search"
            className="bg-transparent h-10 text-xs rounded-lg"
          />
        </div>
        <div className="flex items-center gap-8 w-full bg-[#181818] p-4 px-6 my-4 rounded-lg">
          <div className="flex items-center gap-2">
            <MoneyIcon />
            <p className="flex flex-col">
              <span className="text-[0.75rem] text-[#646464]">Saved sales</span>
              N{addCommasToNumber(Number(total_sales))}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <CountIcon />
            <p className="flex flex-col">
              <span className="text-[0.75rem] text-[#646464]">Count</span>
              {numberOfPendingOrders}
            </p>
          </div>
        </div>
      </>

      <>
        {(isLoading || isFetching) ? (
          <div className="grid p-10 place-content-center w-full">
            <SmallSpinner />
          </div>
        ) : (
          <>
            {sales_transactions?.length ? (
              <Table containerClassName="h-full">
                <TableBody>
                  {sales_transactions?.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium pr-0">
                        <SavedSalesIcon />
                      </TableCell>
                      <TableCell className="font-medium min-w-[180px] max-2xl:pr-0">
                        <p>{transaction.sales_tag ?? "N/A"}</p>
                      </TableCell>
                      <TableCell className="font-medium min-w-[180px] max-2xl:pr-0">
                        <p>{transaction.batch_id}</p>
                        {/* <span className='text-sm text-[#D8D8DF80]'>{transaction.payment_method}</span> */}
                      </TableCell>
                      <TableCell className="">
                        <p className="text-[#F7A325] capitalize">
                          {transaction.status.toLowerCase()}
                        </p>
                        <span className="text-sm text-[#D8D8DF80]">
                          {format(transaction.created_at, "dd LLLL yy")}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <p>
                          ₦{addCommasToNumber(Number(transaction.total_cost))}
                        </p>
                        <span className="text-sm text-[#D8D8DF80] max-w-[50px]">
                          {format(transaction.created_at, "HH:mm:ss")}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          className="min-w-max h-10 bg-[#181818] px-6"
                          onClick={() => {
                            openSingleTxOpen();
                            setSingleInvoice(transaction);
                          }}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                {/* <TableFooter>
                    <TableRow>
                        <TableCell colSpan={3}>Total</TableCell>
                        <TableCell className="text-right">$2,500.00</TableCell>
                    </TableRow>
                    </TableFooter> */}
              </Table>
            ) : (
              <div className="w-full h-full flex items-center justify-center flex-col">
                <svg
                  width="91"
                  height="91"
                  viewBox="0 0 91 91"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="45.5" cy="45.5" r="45.5" fill="#4E3816" />
                  <rect
                    x="16.5454"
                    y="33.1696"
                    width="42.8303"
                    height="46.163"
                    rx="13.1078"
                    transform="rotate(-10.9436 16.5454 33.1696)"
                    fill="white"
                    stroke="#F7A325"
                    stroke-width="0.970948"
                  />
                  <rect
                    x="23.6641"
                    y="42.4521"
                    width="30.9465"
                    height="4.76101"
                    rx="2.3805"
                    transform="rotate(-10.9436 23.6641 42.4521)"
                    fill="#F7A325"
                  />
                  <rect
                    x="25.6523"
                    y="52.7363"
                    width="17.6157"
                    height="4.76101"
                    rx="2.3805"
                    transform="rotate(-10.9436 25.6523 52.7363)"
                    fill="#F7A325"
                  />
                  <rect
                    x="27.6406"
                    y="63.0195"
                    width="17.6157"
                    height="4.76101"
                    rx="2.3805"
                    transform="rotate(-10.9436 27.6406 63.0195)"
                    fill="#F7A325"
                  />
                  <rect
                    x="45.2852"
                    y="48.9404"
                    width="10.9503"
                    height="4.76101"
                    rx="2.3805"
                    transform="rotate(-10.9436 45.2852 48.9404)"
                    fill="#F7A325"
                  />
                  <rect
                    x="47.2734"
                    y="59.2231"
                    width="10.9503"
                    height="4.76101"
                    rx="2.3805"
                    transform="rotate(-10.9436 47.2734 59.2231)"
                    fill="#F7A325"
                  />
                  <rect
                    x="31.0109"
                    y="22.0201"
                    width="42.8303"
                    height="46.163"
                    rx="13.1078"
                    fill="white"
                    stroke="#F7A325"
                    stroke-width="0.970948"
                  />
                  <rect
                    x="36.2383"
                    y="32.4844"
                    width="30.9465"
                    height="4.76101"
                    rx="2.3805"
                    fill="#F7A325"
                  />
                  <rect
                    x="36.2383"
                    y="42.959"
                    width="17.6157"
                    height="4.76101"
                    rx="2.3805"
                    fill="#F7A325"
                  />
                  <rect
                    x="36.2383"
                    y="53.4331"
                    width="17.6157"
                    height="4.76101"
                    rx="2.3805"
                    fill="#F7A325"
                  />
                  <rect
                    x="56.2344"
                    y="42.959"
                    width="10.9503"
                    height="4.76101"
                    rx="2.3805"
                    fill="#F7A325"
                  />
                  <rect
                    x="56.2344"
                    y="53.4331"
                    width="10.9503"
                    height="4.76101"
                    rx="2.3805"
                    fill="#F7A325"
                  />
                </svg>
                <p className="mt-5 text-xs">
                  You currently do not have any saved sales
                </p>
                <p></p>
              </div>
            )}
          </>
        )}
      </>

      {isSingleTxOpen && (
        <SingleTransaction
          closeSingleTxOpen={closeSingleTxOpen}
          isSingleTxOpen={isSingleTxOpen}
          singleInvoice={singleInvoice as ISalesTransactionDto}
        />
      )}
    </>
  );
};

export default SavedSalesTable;
