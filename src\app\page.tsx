'use client'
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { useAuth } from '@/contexts';
import { Paybox360 } from '@/components/icons';


const HomePage = () => {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (!isLoading && !isAuthenticated) {
            router.replace('/login');
        }
        else if (!isLoading && isAuthenticated) {
            router.replace('/dashboard');
        }
    }, [isAuthenticated, isLoading, router]);

    if (isLoading) {
        return <div className='h-screen w-screen flex items-center justify-center'>
            <Paybox360 className='animate-pulse' />
        </div>;
    }

    if (!isLoading && !isAuthenticated) {
        return null;
    }

    return (
        <></>
    );
};

export default HomePage;