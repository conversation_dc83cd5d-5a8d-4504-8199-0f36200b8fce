import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={16} cy={16} r={16} fill="#142D22" />
    <path
      d="M17.0501 15.3873C16.9392 15.3873 16.8284 15.3465 16.7409 15.259C16.5717 15.0898 16.5717 14.8098 16.7409 14.6407L21.5242 9.85734C21.6934 9.68818 21.9734 9.68818 22.1426 9.85734C22.3117 10.0265 22.3117 10.3065 22.1426 10.4757L17.3592 15.259C17.2776 15.3407 17.1667 15.3873 17.0501 15.3873Z"
      fill="#12B669"
    />
    <path
      d="M19.4009 15.8546H16.5834C16.3442 15.8546 16.1459 15.6563 16.1459 15.4171V12.5996C16.1459 12.3604 16.3442 12.1621 16.5834 12.1621C16.8225 12.1621 17.0209 12.3604 17.0209 12.5996V14.9796H19.4009C19.64 14.9796 19.8384 15.1779 19.8384 15.4171C19.8384 15.6563 19.64 15.8546 19.4009 15.8546Z"
      fill="#12B669"
    />
    <path
      d="M17.75 22.2702H14.25C11.0825 22.2702 9.72913 20.9169 9.72913 17.7493V14.2493C9.72913 11.0818 11.0825 9.72852 14.25 9.72852H15.4166C15.6558 9.72852 15.8541 9.92685 15.8541 10.166C15.8541 10.4052 15.6558 10.6035 15.4166 10.6035H14.25C11.5608 10.6035 10.6041 11.5602 10.6041 14.2493V17.7493C10.6041 20.4385 11.5608 21.3952 14.25 21.3952H17.75C20.4391 21.3952 21.3958 20.4385 21.3958 17.7493V16.5827C21.3958 16.3435 21.5941 16.1452 21.8333 16.1452C22.0725 16.1452 22.2708 16.3435 22.2708 16.5827V17.7493C22.2708 20.9169 20.9175 22.2702 17.75 22.2702Z"
      fill="#12B669"
    />
  </svg>
);
export default SVGComponent;
