import {
  CreditCardIcon,
  TransferIcon,
  TwoTonedCopyIcon,
} from "@/components/icons";
import PayboxSmall from "@/components/icons/PayboxSmall";
import {
  Button,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  Skeleton,
} from "@/components/ui";
import { useBooleanStateControl } from "@/hooks";
import { useCartStore, useCompanyStore } from "@/stores";
import { convertNumberToNaira } from "@/utils/currency";
import React, {
  ForwardedRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import toast from "react-hot-toast";
import { UseConfirmSale, UseGetInstantAccount, UseSaveSale } from "../api";
import {
  RegisterSalesData,
  TSalesConfirmationResult,
} from "../api/patchConfirmSale";
import { TCustomer } from "../types";
import ConfirmPaymentCashModal from "./ConfirmPaymentCashModal";
import ConfirmPaymentOthersModal from "./ConfirmPaymentOthersModal";
import ConfirmPaymentTransferModal from "./ConfirmPaymentTransferModal";
import { printReceipt } from "./ReceiptPrintButton";
import { UseUpdateSavedSale } from "../api/postSaveSale";
import { useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import { DiscountShape } from "iconsax-react";
import CreditPaymentModal from "./CreditPaymentModal";
import { useRouter } from "next/navigation";
import { AxiosError } from "axios";

interface checkoutRef {
  paymentMethod: string;
  setPaymentMethod: (e: string) => void;
  setDiscountValue: (v: number) => void;
  discountValue: number;
  overallPrice: number;
  sales_tag: string;
}

const Checkout = React.forwardRef<HTMLDivElement, checkoutRef>(
  (props, ref: ForwardedRef<HTMLDivElement>) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const { branchData } = useCompanyStore();
    const {
      paymentMethod,
      setPaymentMethod,
      setDiscountValue,
      discountValue,
      overallPrice,
      // sales_tag
    } = props;

    const { active_cart, clear_cart } = useCartStore();
    const [accountDetails, setAccountDetails] = useState({
      bank_name: "",
      bank_code: "",
      account_name: "",
      account_number: "",
    });
    const { company, branch } = useCompanyStore();
    const [batchId, setBatchId] = useState("");
    const [splitTransferMethod, setSplitTransferMethod] = useState("");
    const [cashAmount, setCashAmount] = useState<number>();
    const [otherAmount, setOtherAmount] = useState<number>();
    const [transferAmount, setTransferAmount] = useState<number>();
    const [openTransferOptions, setOpenTransferOptions] = React.useState(false);
    const [openSplitPayment, setOpenSplitPayment] = React.useState(false);
    const [selectedCustomer, setSelectedCustomer] = useState<TCustomer>();
    const [salesConfirmationResult, setSuccessResult] =
      React.useState<TSalesConfirmationResult>();
    const [isPartPayment, setPartPayment] = useState(false);

    const [registerSalesResponse, setRegisterSalesResponse] =
      React.useState<RegisterSalesData>();
    const { mutate: getInstantAccount, isPending: isGettingAccountDetails } =
      UseGetInstantAccount();

    // const vatRate = branchData.vat || 0;
    // const [totalPrice, setTotalPrice] = React.useState(
    //   active_cart.reduce(
    //     (sum, item) => sum + Number(item.selling_price) * item.quantity,
    //     0
    //   )
    // );

    const router = useRouter();
    const copyToClipboard = (string: string) => {
      navigator.clipboard.writeText(string);
      toast.success("Copied to clipboard");
    };

    const searchParams = useSearchParams();

    useEffect(() => {
      setBatchId(searchParams.get("batch_id") ?? "");
    }, [searchParams]);
    useEffect(() => {
      if (openSplitPayment) {
        //reset fields
        setCashAmount(0);
        setTransferAmount(0);
        setOtherAmount(0);
      }
    }, [openSplitPayment]);

    React.useEffect(() => {
      if (
        paymentMethod === "TRANSFER" ||
        splitTransferMethod === "SPLIT_TRANSFER"
      ) {
        getInstantAccount(
          {
            company,
            branch,
            batch_id: batchId,
          },
          {
            onSuccess: (data) => {
              setAccountDetails(data.data);
            },
          }
        );
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [paymentMethod, splitTransferMethod]);
    const { mutate: confirmSale, isPending: isConfirmingSale } =
      UseConfirmSale();
    const { mutateAsync: saveSale, isPending: isSavingSale } = UseSaveSale();
    const { mutateAsync: UpdateSaveSale, isPending: isUpdateSale } =
      UseUpdateSavedSale();

    const saveSaleForProcessing = () => {
      setBatchId("");
      saveSale(
        { company, branch, cart: active_cart, save: true },
        {
          onSuccess: () => {
            toast.success("Sale saved successfully for processing");
            clear_cart();
          },
        }
      );
    };
    const {
      state: isCreditPaymentModalOpen,
      setTrue: openCreditPaymentModal,
      setFalse: closeCreditPaymentModal,
    } = useBooleanStateControl();

    const {
      state: isConfirmOthersModalOpen,
      setTrue: openConfirmOthersModal,
      setFalse: closeConfirmOthersModal,
    } = useBooleanStateControl();

    const {
      state: isConfirmCashModalOpen,
      setTrue: openConfirmCashModal,
      setFalse: closeConfirmCashModal,
    } = useBooleanStateControl();

    const {
      state: isConfirmTransferModalOpen,
      setTrue: openConfirmTransferModal,
      setFalse: closeConfirmTransferModal,
    } = useBooleanStateControl();

    const radioOptions: {
      name: string;
      icon: React.JSX.Element;
      value: string;
    }[] = useMemo(
      () => [
        { name: "Card", value: "CARD", icon: <CreditCardIcon /> },
        {
          name: `Transfer ${
            paymentMethod === "TRANSFER"
              ? "via Paybox"
              : paymentMethod === "OTHER_TRANSFER"
              ? "via others"
              : ""
          }`,
          value: "select",
          icon: <TransferIcon />,
        },
        { name: "Cash", value: "CASH", icon: <CreditCardIcon /> },
        { name: "Other", value: "OTHERS", icon: <CreditCardIcon /> },
        { name: "Split", value: "SPLIT", icon: <CreditCardIcon /> },
        { name: "Credit", value: "CREDIT", icon: <DiscountShape /> },
      ],
      [paymentMethod]
    );
    const splitTransferOptions: {
      name: string;
      icon?: React.JSX.Element;
      value: string;
    }[] = useMemo(
      () => [
        { name: "Transfer", value: "SPLIT_TRANSFER" },
        {
          name: "Other transfers",
          value: "SPLIT_OTHER_TRANSFER",
        },
      ],
      []
    );

    const registerSalesForSplitPayment = async () => {
      !batchId &&
        (await saveSale(
          {
            company,
            branch,
            cart: active_cart,
            discount_value: discountValue,
            save: false,
          },
          {
            onSuccess: (data) => {
              //@ts-ignore
              setRegisterSalesResponse(data.data);
              setBatchId(data.data.batch_id);
            },
            onError(error: any) {
              toast.error(error.response?.data.message);
            },
          }
        ));
    };

    const openAppropriateModal = useCallback(async () => {
      const getBatchId = searchParams.get("batch_id") ?? "";

      if (active_cart.length === 0) {
        toast.error("Cart is empty");
        return;
      }

      if (paymentMethod === "CREDIT") {
        openCreditPaymentModal();
      }

      if (
        paymentMethod === "SPLIT" ||
        (paymentMethod === "CREDIT" && isPartPayment)
      ) {
        // Check that at least two payment modes are truthy
        const selectedModes = [cashAmount, otherAmount, transferAmount].filter(
          Boolean
        ).length;
        const selectedModesAmount =
          (cashAmount ?? 0) + (otherAmount ?? 0) + (transferAmount ?? 0);

        if (selectedModesAmount < overallPrice) {
          toast.error("Split payment amount less than total price");
          return;
        }
        if (selectedModesAmount > overallPrice) {
          toast.error("Split payment amount more than total price");
          return;
        }
        if (selectedModes >= 2) {
          openConfirmCashModal();
        } else {
          toast.error("Minimum of two payment modes must be selected");
        }
        return;
      }

      if (paymentMethod === "CARD") {
        saveSaleForProcessing();
        return;
      }
      if (!getBatchId) {
        await saveSale(
          {
            company,
            branch,
            cart: active_cart,
            discount_value: discountValue,
            save: false,
          },
          {
            onSuccess: (data) => {
              //@ts-ignore
              setRegisterSalesResponse(data.data);

              setBatchId(data.data.batch_id);
              if (paymentMethod === "TRANSFER") {
                openConfirmTransferModal();
              } else if (paymentMethod === "OTHERS") {
                openConfirmOthersModal();
                return;
              }
            },
            onError(error: any) {
              toast.error(error.response?.data.message);
            },
          }
        );
      } else {
        await UpdateSaveSale(
          {
            company,
            branch,
            cart: active_cart,
            batch_id: getBatchId,
          },
          {
            onSuccess: (data) => {
              //@ts-ignore
              setRegisterSalesResponse(data.data);

              if (paymentMethod === "TRANSFER") {
                openConfirmTransferModal();
              } else if (paymentMethod === "OTHERS") {
                openConfirmOthersModal();
                return;
              }
            },
            onError(error: any) {
              toast.error(error.response?.data.message);
            },
          }
        );
      }
      if (paymentMethod === "CASH" || paymentMethod === "OTHER_TRANSFER") {
        openConfirmCashModal();
        return;
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
      active_cart,
      saveSale,
      company,
      branch,
      paymentMethod,
      openConfirmTransferModal,
      openConfirmOthersModal,
      openConfirmCashModal,
      discountValue,
      cashAmount,
      transferAmount,
      otherAmount,
    ]);

    const confirm = () => {
      const split_method: Partial<Record<string, boolean | number>> = {};
      if (cashAmount) {
        split_method.cash = true;
        split_method.cash_amount = cashAmount;
      }
      if (otherAmount) {
        split_method.others = true;
        split_method.others_amount = otherAmount;
      }
      if (transferAmount && splitTransferMethod == "SPLIT_TRANSFER") {
        //Or (paymentMethod === "CREDIT" && isPartPayment)
        split_method.transfer = true;
        split_method.transfer_amount = transferAmount;
      }
      if (transferAmount && splitTransferMethod == "SPLIT_OTHER_TRANSFER") {
        //Or (paymentMethod === "CREDIT" && isPartPayment)
        split_method.other_transfer = true;
        split_method.other_transfer_amount = transferAmount;
      }
      const payload: any = {
        company,
        branch,
        batch_id: batchId,
        means_of_payment: paymentMethod,
        customer: selectedCustomer?.id,
      };
      if (
        paymentMethod === "SPLIT" ||
        (paymentMethod === "CREDIT" && isPartPayment)
      ) {
        payload.split_method = split_method;
      }

      confirmSale(payload, {
        onSuccess: (data) => {
          toast.success("Sale confirmed successfully");
          setSuccessResult(data.data);
          setDiscountValue(0);
          clear_cart();
          setBatchId("");
          setSplitTransferMethod("");
          closeCreditPaymentModal();
          
          //#TODO 
          // COMMENTED THIS OUT FOR NOW
          // router.push("/refresh");
          // closeConfirmOthersModal()
          // closeConfirmTransferModal();
        },
        onError(error: unknown) {
          const axiosError = error as AxiosError<{ message: string }>;
          const message = axiosError.response?.data?.message || "Something went wrong!";
          toast.error(message);
        }
      });
    };

    // Add keydown handler only when Checkout div is focused
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        const element = (ref as React.MutableRefObject<HTMLDivElement | null>)
          .current;
        if (element && document.activeElement === element) {
          switch (event.key) {
            case "1":
              setPaymentMethod(radioOptions[0].value); // Card
              break;
            case "2":
              setOpenTransferOptions(true); // Transfer
              break;
            case "3":
              setPaymentMethod(radioOptions[2].value); // Cash
              break;
            case "4":
              setPaymentMethod(radioOptions[3].value); // Other
              break;
            case "Enter":
              openAppropriateModal();
              break;
            default:
              break;
          }
        }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
    }, [setPaymentMethod, openAppropriateModal, radioOptions, ref]);

    const closeCashModal = () => {
      setSelectedCustomer(undefined);
      closeConfirmCashModal();
    };

    return (
      <div
        ref={ref}
        className="sticky bottom-0 bg-[#1C1C1C] min-h-max shrink-0 p-4 2xl:px-8 rounded-xl border-2 border-transparent focus:border-blue-600 focus-within:border-blue-600 outline-none"
        tabIndex={0}
      >
        <iframe
          ref={iframeRef}
          style={{
            display: "none",
            width: "100%",
            height: "0",
            border: "none",
          }}
          title="Print Frame"
        />

        <RadioGroup
          options={radioOptions}
          name=""
          arrangement="row"
          onChange={(e) => {
            if (e !== "select") setPaymentMethod(e);
          }}
          onclick={(e) => {
            if (e === "select") setOpenTransferOptions(true);
            if (e === "SPLIT") {
              setPaymentMethod(e);
              setOpenSplitPayment(true);
              registerSalesForSplitPayment(); //Register Sales
            }
          }}
          value={paymentMethod}
          variant="offwhite"
          itemsContainerClass="!grid grid-cols-2 2xl:grid-cols-3 mb-4"
          className="!max-w-full"
        />
        <Popover
          open={openTransferOptions}
          onOpenChange={setOpenTransferOptions}
        >
          <PopoverTrigger
            asChild
            className="flex-col-reverse sm:flex-row sm:justify-center sm:space-x-2 flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full"
          >
            <span></span>
          </PopoverTrigger>
          <PopoverContent className="flex flex-col p-6 space-y-3 items-stretch w-96">
            <div className="flex justify-between">
              <div>Select transfer option</div>
              <Button
                className="w-2 h-2 rounded-full bg-white/40 grid place-content-center p-3"
                variant={"unstyled"}
                size={"icon"}
                onClick={() => setOpenTransferOptions(false)}
              >
                <X size={12} />
              </Button>
            </div>
            <button
              className="p-3 w-full bg-[#D9D9D91A] text-[#FFFFFF] rounded-xl flex gap-2"
              onClick={() => {
                setPaymentMethod("TRANSFER");
                setOpenTransferOptions(false);
              }}
            >
              <div>
                <PayboxSmall
                  height={24}
                  width={24}
                  className="rounded-[10px]"
                />
              </div>
              <div>
                <div className="flex justify-between text-xs h-6 items-center">
                  <div className="flex">
                    <span>Paybox360 wallet transfer</span>
                  </div>
                  <svg
                    className="pr-1"
                    width="26"
                    height="20"
                    viewBox="0 0 26 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M17.5222 12.3577C18.7806 11.0993 18.8225 9.08518 17.6481 7.77644L17.5222 7.64363L14.0894 4.41139C13.7639 4.08596 13.2363 4.08596 12.9108 4.41139C12.6104 4.7118 12.5873 5.18449 12.8415 5.5114L12.9108 5.58991L16.3437 8.82214C16.9603 9.43876 16.9928 10.4183 16.4411 11.0731L16.3437 11.1792L12.9108 14.4114C12.5854 14.7368 12.5854 15.2645 12.9108 15.5899C13.2112 15.8903 13.6839 15.9134 14.0109 15.6592L14.0894 15.5899L17.5222 12.3577Z"
                      fill="white"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M11.5222 12.3577C12.7806 11.0993 12.8225 9.08518 11.6481 7.77644L11.5222 7.64363L8.08936 4.41139C7.76392 4.08596 7.23628 4.08596 6.91085 4.41139C6.61044 4.7118 6.58733 5.18449 6.84152 5.5114L6.91085 5.58991L10.3437 8.82214C10.9603 9.43876 10.9928 10.4183 10.4411 11.0731L10.3437 11.1792L6.91085 14.4114C6.58541 14.7368 6.58541 15.2645 6.91085 15.5899C7.21125 15.8903 7.68394 15.9134 8.01085 15.6592L8.08936 15.5899L11.5222 12.3577Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <div className="opacity-80 text-[10px] text-start pr-5">
                  Use this transfer option for instant confirmation and quick
                  reconciliation, while building your eligibility for stock
                  overdrafts when needed
                </div>
              </div>
            </button>
            <button
              className="p-3 w-full bg-[#D9D9D91A] text-[#FFFFFF] rounded-xl flex gap-2"
              onClick={() => {
                setPaymentMethod("OTHER_TRANSFER");
                setOpenTransferOptions(false);
              }}
            >
              <div>
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    width="24"
                    height="24"
                    rx="10"
                    fill="#D9D9D9"
                    fill-opacity="0.1"
                  />
                  <path
                    d="M17.8332 16.0833V17.8333H6.1665V16.0833C6.1665 15.7625 6.429 15.5 6.74984 15.5H17.2498C17.5707 15.5 17.8332 15.7625 17.8332 16.0833Z"
                    fill="white"
                    stroke="white"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    opacity="0.6"
                    d="M9.66683 11.416H7.3335V15.4993H9.66683V11.416Z"
                    fill="white"
                  />
                  <path
                    opacity="0.4"
                    d="M11.9998 11.416H9.6665V15.4993H11.9998V11.416Z"
                    fill="white"
                  />
                  <path
                    opacity="0.6"
                    d="M14.3333 11.416H12V15.4993H14.3333V11.416Z"
                    fill="white"
                  />
                  <path
                    opacity="0.4"
                    d="M16.6668 11.416H14.3335V15.4993H16.6668V11.416Z"
                    fill="white"
                  />
                  <path
                    d="M18.4168 18.2715H5.5835C5.34433 18.2715 5.146 18.0732 5.146 17.834C5.146 17.5948 5.34433 17.3965 5.5835 17.3965H18.4168C18.656 17.3965 18.8543 17.5948 18.8543 17.834C18.8543 18.0732 18.656 18.2715 18.4168 18.2715Z"
                    fill="white"
                  />
                  <path
                    d="M17.4657 8.35374L12.2157 6.25375C12.099 6.20708 11.9007 6.20708 11.784 6.25375L6.53401 8.35374C6.32984 8.4354 6.1665 8.67456 6.1665 8.89623V10.8329C6.1665 11.1537 6.429 11.4162 6.74984 11.4162H17.2498C17.5707 11.4162 17.8332 11.1537 17.8332 10.8329V8.89623C17.8332 8.67456 17.6698 8.4354 17.4657 8.35374ZM11.9998 9.9579C11.5157 9.9579 11.1248 9.56707 11.1248 9.0829C11.1248 8.59874 11.5157 8.2079 11.9998 8.2079C12.484 8.2079 12.8748 8.59874 12.8748 9.0829C12.8748 9.56707 12.484 9.9579 11.9998 9.9579Z"
                    fill="white"
                  />
                </svg>
              </div>
              <div>
                <div className="flex justify-between text-xs h-6 items-center">
                  <div className="flex">
                    <span>Other wallet transfer</span>
                  </div>
                  <svg
                    className="pr-1"
                    width="26"
                    height="20"
                    viewBox="0 0 26 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M17.5222 12.3577C18.7806 11.0993 18.8225 9.08518 17.6481 7.77644L17.5222 7.64363L14.0894 4.41139C13.7639 4.08596 13.2363 4.08596 12.9108 4.41139C12.6104 4.7118 12.5873 5.18449 12.8415 5.5114L12.9108 5.58991L16.3437 8.82214C16.9603 9.43876 16.9928 10.4183 16.4411 11.0731L16.3437 11.1792L12.9108 14.4114C12.5854 14.7368 12.5854 15.2645 12.9108 15.5899C13.2112 15.8903 13.6839 15.9134 14.0109 15.6592L14.0894 15.5899L17.5222 12.3577Z"
                      fill="white"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M11.5222 12.3577C12.7806 11.0993 12.8225 9.08518 11.6481 7.77644L11.5222 7.64363L8.08936 4.41139C7.76392 4.08596 7.23628 4.08596 6.91085 4.41139C6.61044 4.7118 6.58733 5.18449 6.84152 5.5114L6.91085 5.58991L10.3437 8.82214C10.9603 9.43876 10.9928 10.4183 10.4411 11.0731L10.3437 11.1792L6.91085 14.4114C6.58541 14.7368 6.58541 15.2645 6.91085 15.5899C7.21125 15.8903 7.68394 15.9134 8.01085 15.6592L8.08936 15.5899L11.5222 12.3577Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <div className="opacity-80 text-[10px] text-start pr-5">
                  With this option, you&apos;ll receive payments directly to
                  your external account and be solely responsible for confirming
                  the transactions
                </div>
              </div>
            </button>
          </PopoverContent>
        </Popover>
        <Popover open={openSplitPayment} onOpenChange={setOpenSplitPayment}>
          <PopoverTrigger
            asChild
            className="flex-col-reverse sm:flex-row sm:justify-center sm:space-x-2 flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full"
          >
            <span></span>
          </PopoverTrigger>
          <PopoverContent className="flex flex-col p-6 space-y-3 items-stretch w-96">
            <div className="flex justify-between">
              <div>Split Payment</div>
              <Button
                className="w-2 h-2 rounded-full bg-white/40 grid place-content-center p-3"
                variant={"unstyled"}
                size={"icon"}
                onClick={() => setOpenSplitPayment(false)}
              >
                <X size={12} />
              </Button>
            </div>

            <div className="text-gray-400 text-[11px]">
              Provide split details for{" "}
              <span className="text-white">
                ({convertNumberToNaira(overallPrice)})
              </span>{" "}
              below to complete payment. {batchId}
            </div>
            <div className="my-1 space-y-1">
              <label className="text-sm">Cash</label>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setCashAmount(Number(e.target.value));
                }}
              />
            </div>
            <div className="my-1 space-y-1">
              <label className="block text-sm">Transfer</label>
              <span className="text-[11px] text-[#A2A2A2]">
                Enter amount and select transfer option
              </span>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setTransferAmount(Number(e.target.value));
                }}
              />
              <RadioGroup
                options={splitTransferOptions}
                name=""
                arrangement="row"
                onChange={(e) => {
                  setSplitTransferMethod(e);
                }}
                onclick={(e) => {
                  setSplitTransferMethod(e);
                }}
                value={splitTransferMethod}
                variant="offwhite"
                itemsContainerClass="!grid grid-cols-2 2xl:grid-cols-2 mb-4"
                className="!max-w-full"
              />
              {splitTransferMethod === "SPLIT_TRANSFER" && (
                <div className="">
                  <div className="flex flex-col justify-center items-center">
                    <p className="text-[0.825rem] text-[#A2A2A2]">Account No</p>

                    <p className="flex items-center justify-between">
                      {isGettingAccountDetails ? (
                        <Skeleton className="w-[150px] h-5 rounded-xl" />
                      ) : (
                        accountDetails.account_number
                      )}
                      <button
                        onClick={() =>
                          copyToClipboard(accountDetails.account_name)
                        }
                        disabled={isGettingAccountDetails}
                      >
                        <TwoTonedCopyIcon />
                      </button>
                    </p>
                  </div>
                  <div className="bg-black rounded-lg grid grid-cols-2 gap-2 py-1">
                    <div className="flex flex-col items-center space-y-1">
                      <p className="text-[10px] text-[#A2A2A2]">Account Name</p>
                      <p className="flex items-center justify-between text-sm text-center">
                        {isGettingAccountDetails ? (
                          <Skeleton className="w-[150px] h-5 rounded-xl" />
                        ) : (
                          accountDetails.account_name
                        )}
                      </p>
                    </div>
                    <div className="flex flex-col items-center space-y-1">
                      <p className="text-[10px] text-[#A2A2A2]">Bank Name</p>
                      <p className="flex items-center justify-between text-sm text-center">
                        {isGettingAccountDetails ? (
                          <Skeleton className="w-[150px] h-5 rounded-xl" />
                        ) : (
                          accountDetails.bank_name
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="my-1 space-y-1">
              <label className="block text-sm">Others</label>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setOtherAmount(Number(e.target.value));
                }}
              />
            </div>
            <div>
              <span className="text-gray-400">Total</span> :{" "}
              <span className="text-white font-medium ml-1">
                {" "}
                {convertNumberToNaira(
                  (cashAmount ?? 0) + (otherAmount ?? 0) + (transferAmount ?? 0)
                )}
              </span>
            </div>
            <button
              className="w-full bg-[#F9F9F9] disabled:bg-muted  disabled:text-white/50 disabled:cursor-not-allowed text-black p-3 rounded-xl"
              onClick={openAppropriateModal}
              disabled={
                active_cart.length === 0 || isSavingSale || isUpdateSale
              }
            >
              Confirm Payment
            </button>
          </PopoverContent>
        </Popover>
        {!openSplitPayment && (
          <button
            className="w-full bg-[#F9F9F9] disabled:bg-muted  disabled:text-white/50 disabled:cursor-not-allowed text-black p-3 rounded-xl"
            onClick={openAppropriateModal}
            disabled={active_cart.length === 0 || isSavingSale || isUpdateSale}
          >
            Make payment ({convertNumberToNaira(overallPrice)})
          </button>
        )}

        {isConfirmOthersModalOpen && (
          <ConfirmPaymentOthersModal
            isModalOpen={isConfirmOthersModalOpen}
            closeModal={closeConfirmOthersModal}
            confirmFunction={confirm}
            isConfirming={isConfirmingSale}
            amount={convertNumberToNaira(overallPrice)}
            branchData={branchData}
            paymentMethod={paymentMethod}
            items={active_cart}
            company={company}
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={setSelectedCustomer}
            batchId={batchId}
            salesConfirmationResult={salesConfirmationResult}
            registerSalesResponse={registerSalesResponse}
            printReceipt={() =>
              printReceipt({
                items: salesConfirmationResult as TSalesConfirmationResult,
                branchData,
                iframeRef,
                paymentMethod,
                selectedCustomer,
                batchId,
                salesConfirmationResult,
              })
            }
          />
        )}

        {isConfirmCashModalOpen && (
          <ConfirmPaymentCashModal
            isModalOpen={isConfirmCashModalOpen}
            closeModal={closeCashModal}
            confirmFunction={confirm}
            isConfirming={isConfirmingSale}
            amount={convertNumberToNaira(overallPrice)}
            branchData={branchData}
            paymentMethod={paymentMethod}
            items={active_cart}
            company={company}
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={setSelectedCustomer}
            batchId={batchId}
            salesConfirmationResult={salesConfirmationResult}
            registerSalesResponse={registerSalesResponse}
            printReceipt={() =>
              printReceipt({
                items: salesConfirmationResult as TSalesConfirmationResult,
                branchData,
                iframeRef,
                paymentMethod,
                selectedCustomer,
                batchId,
                salesConfirmationResult,
              })
            }
          />
        )}
        {isCreditPaymentModalOpen && (
          <CreditPaymentModal
            setSplitTransferMethod={setSplitTransferMethod}
            splitTransferMethod={splitTransferMethod}
            setPartPayment={setPartPayment}
            isPartPayment={isPartPayment}
            cashAmount={cashAmount}
            otherAmount={otherAmount}
            transferAmount={transferAmount}
            setTransferAmount={setTransferAmount}
            setOtherAmount={setOtherAmount}
            setCashAmount={setCashAmount}
            isModalOpen={isCreditPaymentModalOpen}
            closeModal={closeCreditPaymentModal}
            confirmFunction={confirm}
            isConfirming={isConfirmingSale}
            amount={convertNumberToNaira(overallPrice)}
            overallPrice={overallPrice}
            branchData={branchData}
            paymentMethod={paymentMethod}
            items={active_cart}
            company={company}
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={setSelectedCustomer}
            batchId={batchId}
          />
        )}

        {isConfirmTransferModalOpen && (
          <ConfirmPaymentTransferModal
            isModalOpen={isConfirmTransferModalOpen}
            closeModal={closeConfirmTransferModal}
            confirmFunction={confirm}
            isConfirming={isConfirmingSale}
            amount={convertNumberToNaira(overallPrice)}
            batchId={batchId}
            salesConfirmationResult={salesConfirmationResult}
            company={company}
            branchData={branchData}
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={setSelectedCustomer}
            paymentMethod={paymentMethod}
            registerSalesResponse={registerSalesResponse}
            printReceipt={() =>
              printReceipt({
                items: salesConfirmationResult as TSalesConfirmationResult,
                branchData,
                iframeRef,
                paymentMethod,
                selectedCustomer,
                batchId,
                salesConfirmationResult,
              })
            }
          />
        )}
      </div>
    );
  }
);

export default Checkout;

Checkout.displayName = "Checkout";
