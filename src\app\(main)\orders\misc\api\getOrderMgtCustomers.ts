import { useQuery } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';
import { TOrderMgtBranchCustomers } from '../types';

interface fetchBranchOrderCustomersResponse {
  count: number,
  next: string | null,
  previous: string | null,
  results: TOrderMgtBranchCustomers[]
}

interface FetchOrderCustomersOptions {
  branchId: string;
  pageIndex: number;
  pageSize: number;
  startDate?: string | undefined;
  endDate?: string | undefined;
  search?: string;
}

export const getCustomers = async ({
  branchId,
  pageIndex,
  pageSize,
  // startDate,
  // endDate,
  search,
}: FetchOrderCustomersOptions) => {
  const response = await managementAxios.get(
    `/orders/branch/${branchId}/customers?page=${pageIndex}&size=${pageSize}&search=${search}`
    // `/req/get-teams/?company=${companyId}&page=${pageIndex}&size=${pageSize}&timestamp_gte=${startDate}&timestamp_lte=${endDate}&search=${search}`
  );

  return response.data as fetchBranchOrderCustomersResponse
};

export const useGetOrderCustomers = (fetchOptions: FetchOrderCustomersOptions) => {
  const { search, startDate, endDate, pageIndex, pageSize, } = fetchOptions;
  return useQuery({
    queryKey: [`branch-${fetchOptions.branchId}-orders-customers`, fetchOptions, search, startDate, endDate, pageIndex, pageSize],
    queryFn: () => {
      if (fetchOptions) return getCustomers(fetchOptions);
    },
    enabled: !!fetchOptions,
  });
};
