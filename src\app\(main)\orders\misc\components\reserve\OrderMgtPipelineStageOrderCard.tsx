'use client'
import React, { useMemo, useState } from 'react';
import { Draggable, DraggableProvided } from '@hello-pangea/dnd';
import { format } from 'date-fns';
// import toast from 'react-hot-toast';

import { Button, Checkbox, ConfirmActionModal, ConfirmDeleteModal, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { useBooleanStateControl } from '@/hooks';

import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from '../../types';
import OrderDetailsDrawer from './OrderMgtPipelineStageOrderDrawer';
import { convertToTitleCase } from '@/utils/strings';
import { UseMoveOrdertoNextStage } from '../../api';
import { Ellipsis, EllipsisVertical } from 'lucide-react';
import { cn } from '@/utils/classNames';
import { BadgeCheckIcon, BadgeErrorIcon } from '../../icons';

interface ItemProps {
    order: TOrderMgtPipelineStageOrder;
    className?: string
    allOrdersId: string[];
    allOrders: TOrderMgtPipelineStageOrder[];
    index: number;
    stage: OrderMgtPipelineStageDetails
    branch_id: string
    selectedOrders: string[]
    setSelectedOrders: React.Dispatch<React.SetStateAction<string[]>>
    refetchJobData: () => void
    nextStageId?: string
    pipelineId: string
}


const OrderMgtPipelineStageOrderCard: React.FC<ItemProps> = ({ order, allOrders, allOrdersId, index, stage, selectedOrders, setSelectedOrders, className, nextStageId, pipelineId }) => {
    const { order_date, id: order_id, buyer, status, payment_status, amount_paid, order_time } = order
    const ordersIDs = useMemo(() => allOrdersId, [allOrdersId])
    const ordersData = useMemo(() => allOrders, [allOrders])

    const [localIndex, setLocalIndex] = useState(index)
    React.useEffect(() => {
        setLocalIndex(index)
    }, [index])


    const {
        state: isDrawerOpen,
        setTrue: openDrawer,
        setFalse: closeDrawer,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmRescoreModalOpen,
        setTrue: openConfirmRescoreModal,
        setFalse: closeConfirmRescoreModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmProgressModalOpen,
        setTrue: openConfirmProgressModal,
        setFalse: closeConfirmProgressModal,
    } = useBooleanStateControl(false)

    const {
        state: isConfirmDeleteOrderModalOpen,
        setTrue: openConfirmDeleteOrderModal,
        setFalse: closeConfirmDeleteModal,
    } = useBooleanStateControl(false)


    const { mutate: moveOrderToNextStage, isPending: isMovingOrder } = UseMoveOrdertoNextStage()
    const progressOrder = () => {
        if (nextStageId) {
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: [order_id],
                pipeline_id: pipelineId

            })
        }
    }




    return (
        <Draggable draggableId={String(order_id)} index={localIndex}>
            {(provided: DraggableProvided) => (
                <article
                    className={cn('grid grid-cols-[1fr,max-content] gap-1.5 bg-[#1C1C1C] p-4 rounded-xl !text-[0.85rem] mb-4 w-full max-w-[400px]',
                        selectedOrders.includes(order_id) && "border-primary", className
                    )}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                >
                    <section className='flex flex-col gap-2.5'>
                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-xs'>
                            <p className='flex flex-wrap text-[#7D8590]'>
                                Order ID: <span className='text-white font-medium'>#{order_id.substring(0, 8)}</span>
                            </p>
                            <p className='flex flex-wrap text-[#7D8590] text-left truncate'>Time:<span className='text-white font-medium truncate'>{format(new Date(order_date), "hh:mm aa")}</span> </p>
                        </div>{order_time}

                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-[0.675rem]'>
                            <p className='flex flex-col text-[#7D8590]'>
                                <span className='text-white font-medium'>
                                    {order.order_products[0].product_name}{" "}

                                    <span className={cn('inline-flex items-center justify-center font-semibold text-white h-4 min-w-4 p-1 rounded-md bg-background text-[0.6rem]',
                                        order.order_products.length <= 1 && 'hidden'
                                    )}>
                                        {order.order_products.length > 1 && ` +${order.order_products.length - 1}`}
                                    </span>
                                </span>
                                Item:
                            </p>
                            <p className='flex flex-col text-[#7D8590] text-left truncate'><span className='text-white font-medium truncate'>{amount_paid}</span>Amount </p>
                        </div>

                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-[0.675rem]'>
                            <p
                                className={
                                    cn('flex items-center gap-0.5 text-[0.7rem]',
                                    )
                                }
                            >
                                {
                                    payment_status == "unpaid" ?
                                        <BadgeErrorIcon />
                                        :
                                        <BadgeCheckIcon />

                                }
                                {payment_status}
                            </p>
                            <p className='flex items-center gap-1 text-[#7D8590]'>
                                Items:
                                <span className='text-white text-sm font-medium'>
                                    {order.order_products.length - 1}
                                </span>
                            </p>
                        </div>

                        <div className='grid grid-cols-[1fr,0.8fr] gap-4 text-[0.7rem]'>
                            <div className='flex items-center gap-2'>
                                <Button className="text-[0.7rem] px-4  h-8" variant="destructive" size='sm' >Cancel</Button>
                                <Button className="text-[0.7rem] px-4 bg-[#0D5041] text-[#0ADCAC] h-8" variant="unstyled" size='sm' >Accept</Button>
                            </div>

                            <OrderDetailsDrawer
                                allOrdersId={ordersIDs}
                                allOrders={ordersData}
                                closeDrawer={closeDrawer}
                                isDrawerOpen={isDrawerOpen}
                                openDrawer={openDrawer}
                                order_id={order_id}
                                refetchBranchOrders={() => {
                                    //
                                }}
                                stage={stage}
                            />
                        </div>
                    </section>

                    <section className='flex flex-col items-center justify-between ml-auto h-full'>
                        <Popover>
                            <PopoverTrigger className='px-2'>
                                <EllipsisVertical />
                            </PopoverTrigger>

                            <PopoverContent align='end' className='flex flex-col max-w-max p-2 items-stretch'>
                                <button className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text' onClick={openConfirmProgressModal}>
                                    Progress Order
                                </button>
                                <button className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text' onClick={openConfirmDeleteOrderModal}>
                                    Remove Order
                                </button>

                                {
                                    /* {
                                            stage.is_assessment && (
                                                <button onClick={openConfirmSendAssessmentModal} className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text'>
                                                    Send Interview Invite
                                                </button>
                                            )
                                        } */
                                }
                            </PopoverContent>
                        </Popover>
                        <Checkbox
                            checked={selectedOrders.includes(order_id)}
                            className='p-0.5'
                            // circle
                            onCheckedChange={() => {
                                const updatedSelectedOrders = selectedOrders.includes(order_id)
                                    ? selectedOrders.filter(id => id !== order_id)
                                    : [...selectedOrders, order_id];
                                setSelectedOrders(updatedSelectedOrders);
                            }}

                        />
                    </section>





                    {/* /////////////////////////////////////////////////////// */}
                    {/* /////////////////////////////////////////////////////// */}
                    <ConfirmActionModal
                        closeModal={closeConfirmRescoreModal}
                        confirmFunction={() => {
                            //
                        }}
                        // icon={<Rotate fill='white' height={44} width={44} />}
                        isModalOpen={isConfirmRescoreModalOpen}
                        title="Rescore Order"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to rescore <span className='text-header-text font-bold mr-1'>{buyer.first_name}</span>,
                            Please be aware that it can take up to 5 minutes to reflect the new score. It is also possible that the score doesn&apos;t change if
                            the grading criterias like <span className='text-header-text font-medium mr-1'>compulsory requirements, job responsibilities etc.. </span> remain the same.
                        </p>
                    </ConfirmActionModal>


                    <ConfirmActionModal
                        closeModal={closeConfirmProgressModal}
                        confirmFunction={progressOrder}
                        isConfirmingAction={isMovingOrder}
                        isModalOpen={isConfirmProgressModalOpen}
                        title="Move order to next stage"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to move order with Id <span className='text-header-text font-bold mr-1'>#{order.id}</span> to the next stage.
                        </p>
                    </ConfirmActionModal>




                    <ConfirmDeleteModal
                        closeModal={closeConfirmDeleteModal}
                        deleteFunction={() => {
                            //
                        }}
                        isModalOpen={isConfirmDeleteOrderModalOpen}
                        title="Remove Order"
                    >
                        <div className='text-[#8C8CA1] text-sm font-normal'>
                            <p>You are about to delete  <span className='text-header-text font-bold mx-1'>{buyer.first_name}</span> from the pipeline,
                                Please be aware that {buyer.first_name} will be deleted from the pipeline but will still be in the Orders tab.
                            </p>
                        </div>
                    </ConfirmDeleteModal>

                    {/* <LoadingOverlay isOpen={isMovingOrder || isRemovingOrder} /> */}
                </article>
            )}
        </Draggable>
    );
};

export default OrderMgtPipelineStageOrderCard;