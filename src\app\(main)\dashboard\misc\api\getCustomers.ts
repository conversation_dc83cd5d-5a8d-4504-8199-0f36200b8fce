import { useQuery } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';
import { TCustomer } from '../types';
// import { User } from '@/types/users';

type Data = {
    message: string;
    customers: TCustomer[];
    count: number;
    total_customers: number;
};

type ApiResponse = {
    status: string;
    status_code: number;
    data: Data;
    errors: null | string;
};

export const getCustomers = async (company: string, branch: string, search: string): Promise<ApiResponse> => {
    const response = await managementAxios.get(`/api/v1/sales/customers/?company=${company}&branch=${branch}&search=${search}`);
    return response.data
};

export const useGetCustomers = (company: string, branch: string, search: string) => {
    return useQuery({
        queryKey: ['customers-list', company, branch, search],
        queryFn: () => getCustomers(company, branch, search),
    });
};
