'use client'
import axios from 'axios';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';





import { useRouter } from 'next/navigation';
import { TResetPasscode } from '../../reset-passcode/page';
type LoginResponse = {
  id: string;
  token: string;
  message: string;
  status_code: string;
  user: {
    id: string;
  };
};
const API_BASE_URL = 'https://management.libertypayng.com/core';

export const resetPasscode = {
  login: async (data: TResetPasscode): Promise<LoginResponse> => {
    const response = await axios.post<LoginResponse>(`${process.env.NEXT_PUBLIC_API_BASE_URL}/core/reset_sales_default_passcode/`, data);
    return response.data;
  },
};


export const useResetPasscode = () => {
  const router = useRouter();

  return useMutation<LoginResponse, AxiosError<LoginResponse>, TResetPasscode>({
    mutationFn: resetPasscode.login,
    onError: (error) => {
      if (error.response?.status === 401) {
        const responseData = error.response.data;
        if (responseData.status_code === "102" && responseData.message === "User should reset password") {
          router.push('/reset-password');
        }
      }
      // Handle other errors here
    },
    onSuccess: (data) => {
      // Handle successful login here
      // console.log("Login successful", data);
    }
  });
};