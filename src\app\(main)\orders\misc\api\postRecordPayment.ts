import { managementAxios } from "@/lib/axios";
import { useMutation } from "@tanstack/react-query";

interface Props {
    order: string;
    mode_of_transaction: string;
    amount: string | number
    transaction_date: Date
}
const recordPayment = async (DTO: Props) => {
    const res = await managementAxios.post('/orders/payment-record/', DTO)
return res.data
}

export const UseMarkOrderAsPaid = () => {
    return useMutation({
        mutationFn: recordPayment,
        mutationKey: ['recordPayment',]
    })
}