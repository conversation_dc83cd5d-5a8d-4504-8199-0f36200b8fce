import { managementAxios } from "@/lib/axios";
import { useCompanyStore } from "@/stores";
import { InvoiceItemsDtoResponse, TransactionsResponseData } from "@/app/(main)/transactions/misc/types";

import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

interface fetchOptions {
  company: string;
  branch: string;
  pageSize: number;
  pageIndex: number;
  search: string;
  result_type?: string;
  device?: string;
}
export const getSales = async (
  data: fetchOptions
): Promise<TransactionsResponseData> => {

  // const includeSearch =
  //   data.search && data.search.trim() !== "" ? `&search=${data.search}` : "";
  // const { company, branch, pageSize, pageIndex, result_type } = data;
  // console.log(result_type);

  const response = await managementAxios.get(`/api/v1/sales/register/?`, {
    params: data,
  });
  // const response = await managementAxios.get(`/api/v1/sales/register/?company=${company}&branch=${branch}&page=${pageIndex + 1}&size=${pageSize}`);

  return response.data;
};

// export const UseGetSales = (data: fetchOptions) => {
//   return useQuery({
//     queryKey: ["companies-transactions", { ...data }],
//     queryFn: () => getSales(data),
//   });
// };
export const UseGetSales = (fetchOptions: fetchOptions) => {
  return useInfiniteQuery({
    queryKey: ["sales-list", fetchOptions],
    queryFn: ({ pageParam = 1 }) => getSales({...fetchOptions, pageIndex: pageParam,}),
    getNextPageParam: (lastPage, pages) => {
      const totalItems = lastPage?.data.total_transactions;
      const currentItemCount = pages.reduce((acc, page) => acc + page.data.sales_transactions.length, 0);

      if (currentItemCount >= totalItems) {
          return undefined;
      }

      return pages.length + 1;
  },
    // getNextPageParam: (lastPage) => (lastPage as RootResponse).nextPageIndex,
    initialPageParam: 1
  });
};
interface FetchTeamsOptions {
  companyId: string;
  branchId: string;
  referenceId?: string;
  pageIndex: number;
  pageSize: number;
  startDate?: string | undefined;
  endDate?: string | undefined;
  search?: string;
  transaction_status?: string;
}

export const getInvoiceItems = async ({
  companyId,
  branchId,
  referenceId,
  pageIndex,
  pageSize,
  transaction_status,
  // startDate,
  // endDate,
  // search,
}: FetchTeamsOptions): Promise<InvoiceItemsDtoResponse> => {
  const response = await managementAxios.get(
    `/api/v1/sales/transaction/items?company=${companyId}&branch=${branchId}&page=${pageIndex}&size=${pageSize}&search=${referenceId}&transaction_status=${transaction_status}`
  );

  return response.data; //as StockTableResponse;
};

export const useGetInvoiceItems = (fetchOptions: FetchTeamsOptions) => {
  return useQuery({
    queryKey: ['get-invoice-items', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getInvoiceItems(fetchOptions);
    },
    enabled: !!fetchOptions.referenceId,
    // placeholderData: true,
  });
};