export interface User {
  id: string;
}

interface OpeningStock {
  total_stock: number;
  date: string;
  time: string;
}

interface ClosingStock {
  date: string;
  time: string;
  total_stocks: number;
}

interface AccountDetails {
  account_name: string;
  account_number: string;
  bank_name: string;
}
interface ICharges {
  sales_charge: number;
  sales_charge_cap: number;
}
export interface TBranch {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  address: string;
  discount_value: number;
  vat: number;
  is_super_branch: boolean;
  company: string;
  transfer_charges_to_customer: boolean;
  created_by: string;
  updated_by: string | null;
  pos_device: any[];
  user: string;
  charges: ICharges;
  opening_stock: OpeningStock;
  closing_stock: ClosingStock;
  quantity: number;
  stock_on_hold: number;
  stock_value: number;
  sales_value: number;
  expected_profit: number;
  members: any[];
  account_details: AccountDetails;
  phone: string;
}
