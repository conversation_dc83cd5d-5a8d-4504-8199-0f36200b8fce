"use client";
import React from "react";
import { format, format as formatDate, parseISO, subMonths } from "date-fns";
import {
  Button,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RangeDatePicker,
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui";
import { SuccessfulTransfer } from "@/components/icons";

import { PaginationState } from "@tanstack/react-table";
import { convertNumberToNaira } from "@/utils/currency";
import { useCompanyStore } from "@/stores";
import { convertKebabAndSnakeToTitleCase } from "@/utils/strings";
import { DateRange } from "react-day-picker";

import { UseGetSales } from "../api";
import { CountIcon, MoneyIcon } from "../icons";
import { Controller, useForm } from "react-hook-form";
import SingleTransaction from "./SingleTransaction";
import { useBooleanStateControl } from "@/hooks";
import { ISalesTransactionDto } from "@/app/(main)/dashboard/misc/api/getPendingOrders";
import useInfiniteScroll from "react-infinite-scroll-hook";
import LineLoader from "@/components/LineLoader";

const SuccessfulSalesTable = () => {
  const [searchTerm, setSearchTerm] = React.useState<string>("");
  const { company, branch } = useCompanyStore();
  const [resultType, setResultType] = React.useState("TODAY");
  const [transaction_status, setTransaction_status] =
    React.useState("SUCCESSFUl");

  const today = new Date();
  const oneMonthAgo = subMonths(new Date(), 1);

  const [{ pageIndex, pageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: 0,
      pageSize: 100,
    });

  const { control, register } = useForm<{
    searchFilter: string;
    dateFilter: DateRange;
  }>({
    defaultValues: {
      searchFilter: "",
      dateFilter: {
        from: oneMonthAgo,
        to: today,
      },
    },
  });

  const fetchOptions = {
    company,
    branch,
    pageSize: pageSize,
    pageIndex: pageIndex,
    search: searchTerm,
    result_type: resultType,
    transaction_status,
    device: "WEB_POS",
  };

  // const { data, isLoading, isFetching, refetch } = UseGetSales(fetchOptions);
  // const { data, isLoading, isFetching, refetch } = UseGetSales(fetchOptions);
  const {
    data,
    isLoading,
    refetch,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = UseGetSales(fetchOptions);

  const statusColors = [
    { key: "Successful", value: "SUCCESSFUL", color: "#28a745" }, // Green for success
    { key: "In Progress", value: "IN_PROGRESS", color: "#ffc107" }, // Yellow for in-progress
    { key: "Pending", value: "PENDING", color: "#17a2b8" }, // Blue for pending
    { key: "Not Completed", value: "NOT_COMPLETED", color: "#6c757d" }, // Gray for incomplete
    { key: "Cancelled", value: "CANCELLED", color: "#fd7e14" }, // Orange for cancelled
    { key: "Saved", value: "SAVED", color: "#f7a325" }, // Orange for cancelled
    { key: "Failed", value: "FAILED", color: "#dc3545" }, // Red for failure
    { key: "Credit", value: "CREDIT", color: "#dc3545" }, // Red for failure
    { key: "All", value: "", color: "#343a40" }, // Dark gray for all
  ];

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: "0px 0px 800px 0px",
  });

  const available_sales =
    data && data?.pages.flatMap((page) => page?.data?.sales_transactions);
  const available_total_sales =
    data &&
    data?.pages
      .flatMap((page) => page?.data?.total_sales)
      .reduce((acc, current) => acc + current, 0);

  const [singleInvoice, setSingleInvoice] = React.useState<any>();

  const {
    state: isSingleTxOpen,
    setTrue: openSingleTxOpen,
    setFalse: closeSingleTxOpen,
  } = useBooleanStateControl();
  const handleFilterClick = React.useCallback((newResultType: string) => {
    setResultType(newResultType);
  }, []);
  const handleStatusFilterClick = React.useCallback((newStatusType: string) => {
    setTransaction_status(newStatusType);
  }, []);

  React.useEffect(() => {
    refetch();
  }, [resultType, refetch]);

  return (
    <>
      <>
        <div className="sticky top-0 bg-[#1C1C1C] z-[2]">
          <div className=" flex items-center justify-between w-full pl-4 py-4 ">
            <h2 className="font-medium text-xl">Sales</h2>
            <div className="flex gap-4">
              <Input
                placeholder="Search"
                className="bg-transparent h-10 text-xs rounded-lg"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />

              <Popover>
                <PopoverTrigger className="sm:flex-row flex items-center justify-between gap-4 border border-[#262729]  rounded-[7px] py-[9px] md:px-4 text-[#D8D8DF] text-xs">
                  {transaction_status}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M12.3572 11.5217C11.0988 12.7801 9.08469 12.822 7.77595 11.6476L7.64314 11.5217L4.41091 8.08887C4.08547 7.76343 4.08547 7.23579 4.41091 6.91036C4.71131 6.60995 5.184 6.58685 5.51091 6.84103L5.58942 6.91036L8.82165 10.3432C9.43827 10.9598 10.4178 10.9923 11.0727 10.4406L11.1787 10.3432L14.4109 6.91036C14.7363 6.58492 15.264 6.58492 15.5894 6.91036C15.8898 7.21076 15.9129 7.68345 15.6587 8.01036L15.5894 8.08887L12.3572 11.5217Z"
                      fill="white"
                      fill-opacity="0.5"
                    />
                  </svg>
                </PopoverTrigger>

                <PopoverContent
                  align="end"
                  className="flex flex-col max-w-[120px] p-1 space-y-2 items-stretch h-[200px] overflow-auto"
                >
                  {[
                    { key: "Successful", value: "SUCCESSFUL" },
                    { key: "In Progress", value: "IN_PROGRESS" },
                    { key: "Pending", value: "PENDING" },
                    { key: "Not Completed", value: "NOT_COMPLETED" },
                    { key: "Cancelled", value: "CANCELLED" },
                    { key: "Failed", value: "FAILED" },
                    { key: "All", value: "" },
                  ]?.map((v, id) => {
                    return (
                      <Button
                        key={id}
                        className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                        onClick={() => {
                          handleStatusFilterClick(v.value);
                          refetch();
                        }}
                      >
                        {v.key}
                      </Button>
                    );
                  })}
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger className="sm:flex-row flex items-center justify-between gap-4 border border-[#262729]  rounded-[7px] py-[9px] md:px-4 text-[#D8D8DF] text-xs">
                  {resultType}
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M12.3572 11.5217C11.0988 12.7801 9.08469 12.822 7.77595 11.6476L7.64314 11.5217L4.41091 8.08887C4.08547 7.76343 4.08547 7.23579 4.41091 6.91036C4.71131 6.60995 5.184 6.58685 5.51091 6.84103L5.58942 6.91036L8.82165 10.3432C9.43827 10.9598 10.4178 10.9923 11.0727 10.4406L11.1787 10.3432L14.4109 6.91036C14.7363 6.58492 15.264 6.58492 15.5894 6.91036C15.8898 7.21076 15.9129 7.68345 15.6587 8.01036L15.5894 8.08887L12.3572 11.5217Z"
                      fill="white"
                      fill-opacity="0.5"
                    />
                  </svg>
                </PopoverTrigger>

                <PopoverContent
                  align="end"
                  className="flex flex-col max-w-[120px] p-1 space-y-2 items-stretch h-[200px] overflow-auto"
                >
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("TODAY");
                      refetch();
                    }}
                  >
                    Today
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("YESTERDAY");
                      refetch();
                    }}
                  >
                    Yesterday
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("LAST_WEEK");
                      refetch();
                    }}
                  >
                    Last week
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("CURRENT_WEEK");
                      refetch();
                    }}
                  >
                    Current week
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("LAST_MONTH");
                      refetch();
                    }}
                  >
                    Last month
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("THIS_MONTH");
                      refetch();
                    }}
                  >
                    This month
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("LAST_QUARTER");
                      refetch();
                    }}
                  >
                    Last quarter
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("THIS_QUARTER");
                      refetch();
                    }}
                  >
                    This quarter
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("LAST_YEAR");
                      refetch();
                    }}
                  >
                    Last year
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("THIS_YEAR");
                      refetch();
                    }}
                  >
                    This year
                  </Button>
                  <Button
                    className="py-4 rounded-[10px] text-xs w-full !h-0 bg-[#1E1E1E] text-white"
                    onClick={() => {
                      handleFilterClick("CUSTOM");
                      refetch();
                    }}
                  >
                    Custom
                  </Button>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          {isLoading && <LineLoader />}
        </div>
        <div className="flex items-center gap-8 w-full bg-[#181818] p-4 px-6 rounded-lg">
          <div className="flex items-center gap-2">
            <MoneyIcon />
            <p className="flex flex-col">
              <span className="text-[0.75rem] text-[#646464]">
                {resultType} sales
              </span>
              {convertNumberToNaira(available_total_sales || 0)}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <CountIcon />
            <p className="flex flex-col">
              <span className="text-[0.75rem] text-[#646464]">Count</span>
              {(available_sales ?? []).length ?? 0}
            </p>
          </div>
        </div>
      </>

      <Table className="overflow-y-scroll h-full" containerClassName="h-full">
        <TableBody>
          {(available_sales ?? []).map((invoice) => (
            <>
              <TableRow key={invoice.invoice}>
                <TableCell className="font-medium pr-0 max-h-max">
                  <SuccessfulTransfer />
                </TableCell>
                <TableCell className="font-medium min-w-[180px] max-2xl:pr-0">
                  <p>{invoice.sales_tag ?? "N/A"}</p>
                </TableCell>
                <TableCell className="font-medium min-w-[180px] max-2xl:pr-0">
                  <p>{invoice.invoice}</p>
                  <span className="text-sm text-[#D8D8DF80]">
                    {invoice.means_of_payment}
                  </span>
                </TableCell>

                <TableCell className="">
                  <p
                    style={{
                      color: statusColors.find(
                        (v) =>
                          v.value.toLowerCase() === invoice.status.toLowerCase()
                      )?.color as string,
                    }}
                  >
                    {convertKebabAndSnakeToTitleCase(invoice.status)}
                  </p>
                  <span className="text-sm text-[#D8D8DF80]">
                    {invoice.means_of_payment}
                  </span>
                </TableCell>
                <TableCell>
                  <div>
                    <span className="2xl:hidden text-sm text-[#D8D8DF80] max-w-[50px]">
                      {format(invoice.created_at, "dd/MM/yy")}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-[#D8D8DF80] max-w-[50px]">
                      {format(invoice.created_at, "H:mmaa")}
                    </span>
                  </div>
                </TableCell>

                <TableCell className="text-right">
                  <p>
                    {convertNumberToNaira(parseInt(invoice.amount_paid || "0"))}
                  </p>
                </TableCell>

                <TableCell className="text-right">
                  <Button
                    onClick={() => {
                      openSingleTxOpen();
                      setSingleInvoice(invoice);
                    }}
                    variant="ghost"
                    className="min-w-max h-10 bg-[#181818] px-6"
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            </>
          ))}
        </TableBody>
      </Table>
      <div ref={sentryRef} className="mt-10" />
      {isSingleTxOpen && (
        <SingleTransaction
          closeSingleTxOpen={closeSingleTxOpen}
          isSingleTxOpen={isSingleTxOpen}
          singleInvoice={singleInvoice as ISalesTransactionDto}
        />
      )}
    </>
  );
};

export default SuccessfulSalesTable;
