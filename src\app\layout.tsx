'use client'

import './globals.css';

import { DM_Sans, <PERSON>ra, Wix_Madefor_Display } from 'next/font/google';
import localFont from 'next/font/local';
import Script from 'next/script';
import { Toaster } from 'react-hot-toast';

import { cn } from '@/utils/classNames';
import AllProviders from '@/lib/providers';
import { ModalRouteConditionalRenderer } from '@/components/layout';

const fontSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontHeading = Sora({
  subsets: ['latin'],
  variable: '--font-heading',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Sora',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontWixDisplay = Wix_Madefor_Display({
  subsets: ['latin'],
  variable: '--font-wix-display',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontClash = localFont({
  src: './fonts/ClashDisplay-Variable.woff2',
  variable: '--font-clash',
  display: 'swap',
});

// export const metadata = {
//   title: 'Paybox 360',
//   description:
//     'Unlock the simplest of solutions to manage your business and spending.',
// };

export default function RootLayout({
  children,

}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className='dark'>
      <head>
        <link href="/favicon.ico" rel="icon" sizes="any" />

        {/* <!-- Hotjar Tracking Code for Liberty web sales --> */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `(function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:5163170,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,
          }}
          id="show-banner"
        />

      </head>

      <body
        className={cn(
          'font-sans',
          fontSans.variable,
          fontHeading.variable,
          fontWixDisplay.variable,
          fontClash.variable
        )}
      >
        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        />
        <AllProviders>
          {children}
        </AllProviders>
      </body>
    </html>
  );
}
