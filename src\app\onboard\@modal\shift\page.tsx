'use client'

import { MoneyIcon } from '@/app/(main)/history/misc/icons';
import { SmallSpinner } from '@/components/icons';
import { Button, Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, Input, LinkButton } from '@/components/ui'
import { useAuth } from '@/contexts';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils/classNames'
import { format } from 'date-fns';
import { Banknote, Calendar, CreditCardIcon, XIcon } from 'lucide-react';
import React, { useEffect } from 'react'
import { UseEndShift, UseStartShift } from '../../misc/api';
import { useRouter } from 'next/navigation';

const ConfirmPaymentCashModal = () => {
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const { isAuthenticated, isLoading, shiftStatus, refetchShiftStatus, logout } = useAuth();
  const router = useRouter()
  const [cash_in_hand, set_cash_in_hand] = React.useState(0)
  const { mutate: startShift, isPending: isStartingShift } = UseStartShift()
  const { mutate: endShift, isPending: isEndingShift } = UseEndShift()

  const submit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!isLoading && !shiftStatus.shift_started) {
      startShift(cash_in_hand, {
        onSuccess: async () => {
          await refetchShiftStatus()
          setTimeout(() => {
            router.replace('/dashboard')
          }, 1200);
        },
      })
    } else if (!isLoading && shiftStatus.shift_started && !shiftStatus.shift_ended) {
      endShift(cash_in_hand)
      refetchShiftStatus()
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value || '0');
    if (!isNaN(value) && value >= 0) {
      set_cash_in_hand(value);
    }
  };

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/login');
      return;
    }
    else if (isAuthenticated && shiftStatus.ongoing_break) {
      router.replace('/onboard/break');
      return;
    }
  }, [isAuthenticated, isLoading, router, shiftStatus.ongoing_break])

  return (
    <Dialog modal={true} open={true} >
      <DialogContent
        aria-label={"Confirm Cash Payment"}
        className={cn(
          'rounded-[10px]',
          'my-6 bg-[#1C1C1C] !px-0 !pb-0 !pt-4 !max-w-[500px]'
        )}
        style={{
          width: '92.5%',
          minWidth: '300px',
        }}
      >
        <DialogTitle className=' px-6 md:px-8 text-2xl font-semibold'>Shift information</DialogTitle>
        <DialogHeader className={cn('max-sm:sticky top-0 z-10 px-6 md:px-8')}></DialogHeader>

        <form onSubmit={submit}>
          <div className={cn('flex flex-col gap-3 p-6 rounded-xl')}>
            <p className='flex items-center gap-1.5 text-sm text-[#A2A2A2]'>
              Current date and time
            </p>
            <p className='flex items-center gap-2 font-medium text-sm w-full bg-[#181818] p-2 px-4 rounded-md text-[#D8D8DF]'>
              <Calendar className='text-[#D8D8DF]' size={16} />
              {format(new Date(), 'dd/MM/yyyy - hh:mm:ss a')}
            </p>
          </div>

          <div className={cn('flex flex-col gap-3 p-6 rounded-xl')}>
            <p className='flex items-center gap-1.5 text text-[#A2A2A2]'>
              Cash balance
            </p>
            <div className='flex items-center gap-5 font-medium text-sm w-full bg-[#181818] p-2 px-4 rounded-md text-[#D8D8DF]'>
              <p>NGN</p>
              <Input
                placeholder="Enter amount (leave empty if there's no cash)"
                className='bg-transparent h-12 text-sm rounded-lg min-w-lg'
                containerClassName='w-full grow'
                type='number'
                pattern="[0-9]*"
                value={cash_in_hand === 0 ? '' : cash_in_hand}
                onChange={handleChange}
              />
            </div>
          </div>

          <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8 text-[0.9rem]'>
            <Button type="submit" className='py-6 w-full' ref={buttonRef}>
              {
                !shiftStatus.shift_started ?
                  'Start shift'
                  :
                  isStartingShift ?
                    'Starting shift...'
                    :
                    shiftStatus.shift_started && !shiftStatus.shift_ended ?
                      'End shift'
                      :
                      isEndingShift ?
                        'Ending shift...'
                        :
                        'Shift ended'
              }
              {
                (isStartingShift || isEndingShift) &&
                <SmallSpinner color='black' className='ml-2' />
              }
            </Button>

            <Button className={cn('py-6 w-full', (!shiftStatus.shift_started || !shiftStatus.shift_ended) && "hidden")} onClick={logout} >
              Logout
            </Button>
            {
              shiftStatus.shift_started && !shiftStatus.shift_ended &&
              <LinkButton href="/dashboard" className={cn('py-6 w-full')} >
                Resume shift
              </LinkButton>
            }
          </DialogFooter>
        </form>

        {/* <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8 text-[0.9rem]'>

        </DialogFooter> */}
      </DialogContent>
    </Dialog>
  )
}

export default ConfirmPaymentCashModal