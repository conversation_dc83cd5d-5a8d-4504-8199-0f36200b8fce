import React, { useEffect, useState } from "react";
import { Item, OrderDetail, Product } from "../../types/instore";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
} from "@/components/ui/";
import { StatusEnum } from "./OrderMgtPipelineStageOrderCard";
import { cn } from "@/utils/classNames";
import { useGetBranchOrderDetails } from "../../api/getBranchOrders";
import { useCompanyStore } from "@/stores";
import { SmallSpinner } from "@/components/icons";
import { UseApproveOrder } from "../../api/postAcceptOrder";
import toast from "react-hot-toast";
import { useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import moment from "moment";

const OrderMgtPipelineStageOrderTrail = ({
  orderId,
  closeDrawer,
}: {
  orderId: string;
  closeDrawer: () => void;
}) => {
  const { company, branch } = useCompanyStore();
  const queryClient = useQueryClient();
  const [statusData, setStatusData] = useState<
    {
      id: StatusEnum;
      status: string;
      sections: any;
    }[]
  >();
  const { data: orderDetail, isLoading } = useGetBranchOrderDetails(
    branch,
    company,
    orderId
  );
  const { mutateAsync, isPending } = UseApproveOrder();
  const order_details = orderDetail?.data?.order_details as OrderDetail;
  useEffect(() => {
    const newOrder = order_details?.trail["new_order"] ?? [];
    const fulfilledOrder = order_details?.trail["fulfilled"] ?? [];
    const completedOrder = order_details?.trail["completed"] ?? [];
    const cancelledOrder = order_details?.trail["cancelled"] ?? [];
    console.log(newOrder);

    setStatusData([
      {
        id: StatusEnum.NewOrder,
        status: "New Orders",
        sections: newOrder ?? [],
      },
      {
        id: StatusEnum.Fulfilled,
        status: "Fulfilled Orders",
        sections: fulfilledOrder ?? [],
      },
      {
        id: StatusEnum.Completed,
        status: "Completed Orders",
        sections: completedOrder ?? [],
      },
      {
        id: StatusEnum.Cancelled,
        status: "Cancelled Orders",
        sections: cancelledOrder ?? [],
      },
    ]);
  }, [order_details]);

  const OrdersToBeUpdated = order_details?.products.filter((v) => v.bar_alert);

  const handleApproveOrder = async () => {
    try {
      await mutateAsync(
        {
          company,
          branch,
          batch_id: orderId,
        },
        {
          onSuccess() {
            toast.success("Order Update Approved");
            queryClient.refetchQueries({ queryKey: ["branch-order"] });
            queryClient.refetchQueries({ queryKey: ["branch-order-details"] });
            closeDrawer();
          },
        }
      );
    } catch (error) {
      console.log(error);
    }
  };

  const removalTheme = "bg-[#EF4444] text-[#fff]"
  const reductionTheme = "bg-[#C78C00] text-[#000]"


  return (
    <>
      {isLoading ? (
        <div className="grid place-content-center">
          <SmallSpinner />
        </div>
      ) : (
        <div className="space-y-2">
          {OrdersToBeUpdated.length > 0 && (
            <div className={cn(order_details?.reduction && "bg-[#2E250F]",  order_details?.removal && "bg-[#220E0E]", "pl-10 py-4  rounded-lg")}>
              <div className="flex justify-between items-center pr-4">
                <h1 className={cn(order_details?.reduction && "text-[#C78C00]",  order_details?.removal && 'text-[#EF4444]' )}>Order Update Request</h1>
                <Button
                  className={cn( order_details?.reduction && reductionTheme, order_details?.removal && removalTheme,  "h-fit")}
                  onClick={handleApproveOrder}
                >
                  {isPending ? <SmallSpinner /> : "Approve"}
                </Button>
              </div>
              <ProductTable items={OrdersToBeUpdated as Product[]} />
            </div>
          )}

          <div className="relative pl-10 py-4 bg-zinc-900 rounded-lg">
            {(statusData ?? [])?.map((status, index) => {
              const isLast = index === (statusData ?? [])?.length - 1;
              return (
                <Accordion
                  key={status.id}
                  type="single"
                  collapsible
                  className="relative pl-4"
                >
                  {/* Vertical line */}
                  {!isLast && (
                    <div className="absolute left-0 top-0 bottom-0 w-px bg-zinc-700 z-0" />
                  )}

                  {/* Dot */}
                  <StatusDot status={status.id} />

                  {/* Content */}
                  <AccordionItem
                    value={status.id}
                    className="bg-zinc-900 rounded-lg px-4 pb-3 border-none"
                  >
                    <AccordionTrigger className="text-left flex pt-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold">{status.status}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <ProductTable items={status.sections as Item[]} />
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              );
            })}
          </div>
        </div>
      )}
    </>
  );
};

const ProductTable = ({ items }: { items: Item[] | Product[] }) => (
  <>
    {items.length > 0 ? (
      <div className="mt-4 space-y-2">
        <table className="w-full text-sm text-gray-200 mt-2">
          <thead>
            <tr className="text-gray-400 border-b border-zinc-700 ">
              <th className="text-left py-1 font-light">Item detail</th>
              <th className="text-left py-1 font-light">Quantity</th>
              <th className="text-left p-1 font-light">Price</th>
              <th className="text-left py-1 font-light">Updated On</th>
            </tr>
          </thead>
          <tbody>
            {items.map((item: any, i) => (
              <tr key={i}>
                <td className="py-1 font-light text-gray-400">
                  {item.item_description}
                </td>
                <td className="py-1 font-semibold">{item.quantity}</td>
                <td className="p-1 font-semibold">
                  ₦{(item.unit_price ?? item.amount)?.toLocaleString()}
                </td>
                <td className="py-1 text-xs ">
                  {moment(item?.created_at || item.updated_at).format("lll")}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    ) : (
      <div className="p-8 grid place-content-center text-gray-400">
        Trail Empty
      </div>
    )}
  </>
);
const StatusDot = ({ status }: { status: StatusEnum }) => {
  return (
    <div className="absolute left-[-13px] top-0 z-10">
      <div
        className={cn(
          `w-7 h-7 rounded-full bg-white grid place-content-center`
        )}
      >
        {status !== StatusEnum.Completed ? (
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.05176 11.9577L9.20292 13.1088L12.2727 10.0391"
              stroke="#272727"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.58127 5.2807H11.651C13.1859 5.2807 13.1859 4.51326 13.1859 3.74582C13.1859 2.21094 12.4185 2.21094 11.651 2.21094H8.58127C7.81383 2.21094 7.04639 2.21094 7.04639 3.74582C7.04639 5.2807 7.81383 5.2807 8.58127 5.2807Z"
              stroke="#272727"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M13.1857 3.76172C15.7413 3.89986 17.0229 4.84381 17.0229 8.35102V12.9557C17.0229 16.0254 16.2555 17.5603 12.4183 17.5603H7.81364C3.97643 17.5603 3.20898 16.0254 3.20898 12.9557V8.35102C3.20898 4.85149 4.49061 3.89986 7.04619 3.76172"
              stroke="#272727"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ) : (
          <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14.2498 17.0626C13.0348 17.0626 11.8873 16.4326 11.2423 15.3976C10.8973 14.8651 10.7023 14.2276 10.6873 13.5751C10.6648 12.4801 11.1298 11.4601 11.9623 10.7701C12.5848 10.2526 13.3648 9.96012 14.1673 9.93762C15.1423 9.93012 16.0198 10.2676 16.7098 10.9276C17.3998 11.5876 17.7898 12.4726 17.8048 13.4251C17.8198 14.0776 17.6548 14.7151 17.3248 15.2776C17.1448 15.5926 16.9123 15.8851 16.6348 16.1326C16.0198 16.7101 15.1948 17.0476 14.3173 17.0626C14.3023 17.0626 14.2798 17.0626 14.2498 17.0626ZM14.2498 11.0626C14.2348 11.0626 14.2123 11.0626 14.1973 11.0626C13.6423 11.0776 13.1173 11.2726 12.6823 11.6326C12.1123 12.1051 11.7973 12.8026 11.8123 13.5526C11.8198 13.9951 11.9548 14.4301 12.1873 14.7976C12.6448 15.5326 13.4398 15.9826 14.2948 15.9376C14.8873 15.9226 15.4498 15.6976 15.8773 15.3001C16.0723 15.1276 16.2298 14.9326 16.3498 14.7226C16.5748 14.3326 16.6873 13.8976 16.6798 13.4551C16.6648 12.8026 16.4023 12.1951 15.9298 11.7451C15.4798 11.3026 14.8798 11.0626 14.2498 11.0626Z"
              fill="#272727"
            />
            <path
              d="M13.8377 14.8132C13.6952 14.8132 13.5601 14.7607 13.4476 14.6557L12.6902 13.9358C12.4652 13.7183 12.4576 13.3658 12.6751 13.1408C12.8926 12.9158 13.2451 12.9082 13.4701 13.1257L13.8377 13.4782L15.0151 12.3382C15.2401 12.1207 15.5926 12.1282 15.8101 12.3532C16.0276 12.5782 16.0201 12.9308 15.7951 13.1483L14.2276 14.6632C14.1151 14.7607 13.9727 14.8132 13.8377 14.8132Z"
              fill="#272727"
            />
            <path
              d="M9 9.97563C8.9025 9.97563 8.805 9.95315 8.715 9.90065L2.09251 6.06816C1.82251 5.91066 1.7325 5.56564 1.89 5.29564C2.0475 5.02564 2.39251 4.93562 2.65501 5.09312L8.9925 8.76063L15.2925 5.11564C15.5625 4.95814 15.9075 5.05566 16.0575 5.31816C16.215 5.58816 16.1175 5.93313 15.855 6.09063L9.2775 9.90065C9.195 9.94565 9.0975 9.97563 9 9.97563Z"
              fill="#272727"
            />
            <path
              d="M9 16.7713C8.6925 16.7713 8.4375 16.5163 8.4375 16.2088V9.40625C8.4375 9.09875 8.6925 8.84375 9 8.84375C9.3075 8.84375 9.5625 9.09875 9.5625 9.40625V16.2088C9.5625 16.5163 9.3075 16.7713 9 16.7713Z"
              fill="#272727"
            />
            <path
              d="M9.00015 17.0639C8.34015 17.0639 7.68015 16.9214 7.17015 16.6289L3.16515 14.4089C2.07765 13.8089 1.22266 12.3614 1.22266 11.1164V6.87139C1.22266 5.62639 2.07765 4.18642 3.16515 3.57892L7.17015 1.35891C8.19015 0.781406 9.79515 0.781406 10.8226 1.35891L14.8276 3.57892C15.9151 4.17892 16.7701 5.62639 16.7701 6.87139V11.1164C16.7701 11.1914 16.7702 11.2514 16.7551 11.3264C16.7176 11.5214 16.5752 11.6864 16.3877 11.7464C16.2002 11.8139 15.9901 11.7689 15.8326 11.6414C14.9701 10.8914 13.6352 10.8614 12.7277 11.5889C12.1502 12.0464 11.8127 12.7439 11.8127 13.4864C11.8127 13.9289 11.9327 14.3639 12.1652 14.7389C12.2252 14.8439 12.2852 14.9264 12.3527 15.0089C12.4652 15.1364 12.5102 15.3089 12.4802 15.4739C12.4502 15.6389 12.3452 15.7814 12.1952 15.8639L10.8226 16.6214C10.3126 16.9214 9.66015 17.0639 9.00015 17.0639ZM9.00015 2.06391C8.53515 2.06391 8.06264 2.16141 7.72514 2.34891L3.72015 4.56893C2.99265 4.96643 2.36265 6.04639 2.36265 6.87139V11.1164C2.36265 11.9414 3.00015 13.0214 3.72015 13.4189L7.72514 15.6389C8.40764 16.0214 9.60015 16.0214 10.2826 15.6389L11.1226 15.1739C10.8451 14.6714 10.6952 14.0864 10.6952 13.4864C10.6952 12.3914 11.1827 11.3789 12.0302 10.7039C13.0502 9.88642 14.5051 9.71389 15.6526 10.2014V6.85642C15.6526 6.03142 15.0152 4.95141 14.2952 4.55391L10.2902 2.3339C9.93765 2.1614 9.46515 2.06391 9.00015 2.06391Z"
              fill="#272727"
            />
          </svg>
        )}
      </div>
    </div>
  );
};
export default OrderMgtPipelineStageOrderTrail;
