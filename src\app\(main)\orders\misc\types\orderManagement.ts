
export interface OrderMgtPipelineDataResult {
    pipeline_id: string;
    pipeline_name: string;
    delivered_orders_count: number;
    cancelled_orders_count: number;
    total_orders_count: number;
    stages: OrderMgtPipelineStageDetails[]
}

export interface OrderMgtPipelineStageDetails {
    stage_id: string;
    stage_name: string;
    email_notification_enabled: boolean;
    order: number;
    email_subject: undefined | string;
    email_body: undefined | string;
    email_text: undefined | string;
    position: number;
    order_count: number
    data: TOrderMgtPipelineStageOrder[];
};

export interface TOrderMgtPipelineStageOrder {
    id: string;
    amount_paid: string;
    status: string;
    payment_status: string;
    buyer: TOrderMgtPipelineStageOrderBuyer;
    order_date: string;
    order_time: string;
    channels: string;
    current_stage: TOrderMgtCurrentStage;
    order_products: TOrderMgtBranchOrderProduct[];
    trail: {
        events: TorderMgtOrderTrailEvent[];
    }
    shipping: number;
    discount: number;
    tax: number;
    contact_name: string;
    contact_phone_number: string;
    additional_information: string;
    ship_to_different_address: boolean;
    total_price: number;
    whatsapp_url: string;
    is_delivered: boolean;
    is_cancelled: boolean;
    mode_of_transaction: string | null
}

export interface TOrderMgtPipelineStageOrderBuyer {
    id: string;
    created_at: string;
    updated_at: string;
    first_name: string;
    middle_name: string;
    last_name: string;
    country: string;
    city: string;
    state: string;
    email: string;
    phone_number: string;
    address: string;
    postal_code: string | null;
    status: string;
    ship_to_different_address: boolean;
}


export interface TOrderMgtBranchCustomers {
    name: string,
    email: string,
    phone_number: string,
    total_orders: number,
    last_order: string
}
export interface TOrderMgtBranchOrderHistory {
    item: string,
    email: string,
    current_stage: string,
    payment_status: "paid" | "unpaid",
    last_updated: string
}



interface TOrderMgtCurrentStage {
    id: string;
    created_at: string;
    updated_at: string;
    name: string;
    email_subject: string | null;
    email_body: string | null;
    email_text: string | null;
    email_notification_enabled: boolean;
    order_count: number | null;
    position: number;
    pipeline: string;
}

interface TOrderMgtBranchOrderProduct {
    product_name: string;
    product_description: string;
    product_img: string | null;
    quantity: number;
    price: string;
    sub_total: string;
    payment_option: string;
}


export type TorderMgtOrderTrailEvent = {
    event: string;
    timestamp: string;
    stage_name: string;
};


export type TBuyer = {
    id: string;
    created_at: string;
    updated_at: string;
    first_name: string;
    middle_name: string;
    last_name: string;
    country: string;
    city: string;
    state: string;
    email: string;
    phone_number: string;
    address: string;
    postal_code: string | null;
    status: string;
    ship_to_different_address: boolean;
};

export type TProductOrder = {
    buyer_name: string;
    email: string;
    order_status: string;
    order_date: string;
    order_details: TOrderMgtPipelineStageOrder;
};


