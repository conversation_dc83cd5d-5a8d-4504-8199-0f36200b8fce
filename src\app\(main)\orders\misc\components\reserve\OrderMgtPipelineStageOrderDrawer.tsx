'use client'

import * as React from "react"
// import toast from "react-hot-toast"

import { <PERSON><PERSON>, <PERSON>er<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, } from "@/components/ui"
// import { getInitials } from "@/utils/strings"
import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from "../../types"
// import { DoubleForward } from "@/icons/core"
import OrderMgtPipelineStageOrderTrail from "./OrderMgtPipelineStageOrderTrail"
import OrderMgtPipelineStageOrderDetails from "./OrderMgtPipelineStageOrderDetails"
import { ArrowLeft, ArrowRight, ChevronLeft, ChevronRight, XIcon } from "lucide-react"
import { cn } from "@/utils/classNames"
import { UseAcceptOrder, UseCancelOrder } from "../../api"
import { SmallSpinner } from "@/components/icons"



interface props {
    order_id: string
    allOrdersId: string[]
    allOrders: TOrderMgtPipelineStageOrder[]
    isDrawerOpen: boolean
    refetchBranchOrders: () => void
    closeDrawer: () => void
    openDrawer: () => void
    stage: OrderMgtPipelineStageDetails
    viewOnly?: boolean
    progressOrder?: () => void
}





const OrderDetailsDrawer: React.FC<props> = ({ openDrawer, closeDrawer, allOrders, allOrdersId, order_id, refetchBranchOrders }) => {

    const [currentOrderIndex, setCurrentOrderIndex] = React.useState<number>(allOrdersId.findIndex((orderId) => orderId === order_id));
    const currentOrder = allOrders[currentOrderIndex];

    const moveToNext = () => {
        setCurrentOrderIndex((prevIndex) => (prevIndex + 1) % allOrdersId.length);
    };

    const moveToPrevious = () => {
        setCurrentOrderIndex((prevIndex) =>
            prevIndex === 0 ? allOrdersId.length - 1 : prevIndex - 1
        );
    };
    // const tabListRef = React.useRef<HTMLDivElement>(null);

    // const [currentOrder, setCurrentOrder] = React.useState<number>(order_id)
    // const currentOrderIndex = allOrders.findIndex(
    //     (Order) => Order === currentOrder
    // );
    // const nextOrder =
    //     currentOrderIndex + 1 < allOrders.length
    //         ? allOrders[currentOrderIndex + 1]
    //         : allOrders[0];
    // const previousOrder =
    //     currentOrderIndex > 0
    //         ? allOrders[currentOrderIndex - 1]
    //         : allOrders[allOrders.length - 1];

    // const moveToNext = () => {
    //     setCurrentOrder(nextOrder);
    //     setCurrentTab(prev => prev)
    // };

    // const moveToPrevious = () => {
    //     setCurrentOrder(previousOrder);
    // };

    // React.useEffect(() => {
    //     setCurrentOrder(order_id)
    // }, [order_id])



    // const { data: basicDetails, isLoading: isLoadingDetails, refetch: refetchBasicDetails } = useQuery(
    //     [`Order-basic-details-${currentOrder}`, currentOrder],
    //     async () => {
    //         const response = await Axios.get(`/recruiter/Order_applied_job_detail/${currentOrder}/`);
    //         return response.data?.job_application as OrderDetails;
    //     },
    //     {
    //         enabled: isDrawerOpen,
    //     }
    // );



    // const { mutate: markASeen, isLoading: isMarkingAsSeen } = useEditIsSeen(basicDetails?.job.id || 0)
    // const markOrderAsSeen = () => {
    //     if (currentMatch && isNaN(currentMatch)) {
    //         toast.error("Enter a valid number")
    //     }
    //     markASeen(
    //         { id: currentOrder, is_seen: true },
    //         {
    //             onSuccess() {
    //                 toast.success("Marked as seen")
    //                 refetchJobData()
    //                 refetchBasicDetails()
    //             },
    //             onError() {
    //                 toast.error("Encountered marking Order as seeen")
    //             },
    //         }
    //     )
    // }

    // const markAsSeenAndMoveToNext = () => {
    //     markASeen(
    //         { id: currentOrder, is_seen: true },
    //         {
    //             onSuccess() {
    //                 refetchJobData()
    //                 refetchBasicDetails()
    //                 moveToNext()
    //             },
    //             onError() {
    //                 toast.error("Encountered marking Order as seeen")
    //             },
    //         }
    //     )
    // }




    // React.useEffect(() => {
    //     if (isDrawerOpen) {
    //         refetchBasicDetails();
    //     }
    // }, [currentOrder]);

    const { mutate: acceptOrderFn, isPending: isAcceptingOrder } = UseAcceptOrder()
    const { mutate: cancelOrderFn, isPending: isCancellingOrder } = UseCancelOrder()
    const acceptOrder = () => {
        acceptOrderFn({ order_ids: [order_id] }, {
            onSuccess() {
                refetchBranchOrders()
            },
        })
    }
    const cancelOrder = () => {
        cancelOrderFn({ order_ids: [order_id] }, {
            onSuccess() {
                refetchBranchOrders()
            },
        })
    }


    const close = () => {
        closeDrawer()
    }

    const tabsArray = [
        {
            title: "Order details",
            value: "details",
            content: <OrderMgtPipelineStageOrderDetails data={currentOrder} order_id={order_id} />
        },
        {
            title: "Order trails",
            value: "trails",
            content: <OrderMgtPipelineStageOrderTrail />
        }
    ]






    return (
        <Drawer direction={"right"} dismissible onClose={() => close()}>
            <DrawerTrigger className="max-w-max px-4 py-1.5 m-0" asChild >
                <Button className="text-[0.7rem] px-6 bg-[#373737] h-8" variant="secondary" size='sm' onClick={() => openDrawer()}>View</Button>
            </DrawerTrigger>

            <DrawerContent className="w-full md:w-[60%] lg:w-[50%] md:right-0 md:left-auto md:max-w-[650px] h-[90vh] md:h-screen !m-0 !p-0 bg-background text-white border-none rounded-l-2xl overflow-hidden">
                <div className="grow flex flex-col relative max-h-full overflow-y-hidden">
                    <div className="w-full flex items-center justify-center bg-primary md:hidden">
                        <div className="mx-auto mt-3 mb-1 h-1 w-[100px] rounded-full bg-white/60" />
                    </div>

                    <header className="sticky top-0 flex items-center justify-between w-full max-md:pt-1 px-5 md:px-8 gap-1.5 p-4 text-center sm:text-left border-b-[1px] border-[#C1C3C7]">
                        <h3 className="font-medium text-white">Order Details</h3>
                        <DrawerClose className="text-sm flex items-center justify-center bg-[#F2F5FF33] h-7 w-7 rounded-full font-medium text-white"><XIcon size={15} /></DrawerClose>
                    </header>


                    <section className="grow flex flex-col overflow-y-scroll px-4 md:px-6">
                        <header className="flex items-center gap-4 p-6 pb-4">
                            <Button className="bg-[#373737] h-10 rounded-md" variant="secondary">
                                Mark as read
                            </Button>
                            <Button className="border-[#787878] border-[0.4px] h-10 rounded-md" variant="outline" >
                                Request payment
                            </Button>
                            <Button className="border-[#787878] border-[0.4px] h-10 rounded-md" variant="outline" onClick={cancelOrder}>
                                Cancel order
                                {
                                    isCancellingOrder && <SmallSpinner className="ml-2" />
                                }
                            </Button>
                            <Button className="bg-white border-white border-[0.4px] h-10 rounded-md text-black" variant="unstyled" onClick={acceptOrder}>
                                Accept order
                                {
                                    isAcceptingOrder && <SmallSpinner className="ml-2" />
                                }
                            </Button>
                        </header>


                        <div>
                            <Tabs defaultValue="details">
                                <TabsList className='flex items-center justify-start gap-4 w-full border-b-[0.3px] border-[#C1C3C7] bg-transparent p-0 rounded-none'>
                                    {
                                        tabsArray.map((tab, index) => (
                                            <TabsTrigger className="!bg-transparent py-2.5 px-5 !border-b-2 border-transparent data-[state=active]:[border-style:solid_!important] data-[state=active]:border-white rounded-none" key={index} value={tab.value} >
                                                {tab.title}
                                            </TabsTrigger>
                                        ))
                                    }
                                </TabsList>

                                {
                                    tabsArray.map((tab, index) => (
                                        <TabsContent className="p-4 px-0" key={index} value={tab.value}>
                                            {tab.content}
                                        </TabsContent>
                                    ))
                                }
                            </Tabs>
                        </div>
                    </section>


                    <DrawerFooter className="sticky bottom-0 flex flex-row items-center justify-between flex-wrap bg-main-bg w-full text-white md:px-8 rounded-t-2xl ">
                        <Button size='sm' variant='outline' onClick={moveToPrevious}>
                            <ChevronLeft /> Prev
                        </Button>

                        <div className="flex items-center flex-wrap gap-4">
                            {
                                <>
                                    <Button size='sm' variant='outline'>
                                        Move to next stage
                                    </Button>
                                    <Button className={cn("text-primary",)} size='sm' variant='unstyled'>
                                        Remove
                                    </Button>
                                </>
                            }
                        </div>


                        <Button size='sm' variant='outline' onClick={moveToNext}>
                            Next
                            <ChevronRight />
                        </Button>
                    </DrawerFooter>
                </div>
            </DrawerContent>
        </Drawer >
    )
}


export default OrderDetailsDrawer