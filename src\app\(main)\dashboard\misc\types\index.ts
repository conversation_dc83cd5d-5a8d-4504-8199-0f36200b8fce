import { faker } from "@faker-js/faker";

export interface TProductt {
  category: string;
  category_id: string;
  subcategory_id?: string;
  item: string;
  item_id: string;
  quantity: number;
  stock_price: string;
  sales_tag?: string;
  selling_price: string;
  image: string | null;
  branch?: string
}
// product_vat, subcategory_id, sku, price_tag, quantity_soldts
export interface TProduct {
  branch?: string
  category: string;
  category_id: string;
  item: string;
  item_id: string;
  description?: string;
  image: string | null;
  product_vat?: string;
  subcategory?: string;
  subcategory_id?: string;
  sku?: string;
  price_tag?: string;
  selling_price: string;
  stock_price: string;
  quantity: number;
  quantity_sold?: number;
}


export interface TCartItem extends TProduct {
  quantity: number;
}

function generateMockProduct(): TProduct {
  const branches = Array.from(
    { length: faker.datatype.number({ min: 1, max: 10 }) },
    () => faker.location.city()
  );

  return {
    item_id: faker.string.uuid(),
    item: faker.commerce.productName(),
    image: faker.image.url(),
    quantity: faker.datatype.number({ min: 1, max: 100 }),
    selling_price: faker.commerce.price(),
    stock_price: faker.commerce.price(),
    category: faker.commerce.department(),
    category_id: faker.string.uuid(),
    description: faker.commerce.productDescription(),
    product_vat: faker.commerce.price({ min: 0, max: 20, dec: 2 }),
    subcategory: faker.commerce.department(),
    subcategory_id: faker.string.uuid(),
    sku: faker.string.alphanumeric(10),
    price_tag: faker.commerce.price(),
    quantity_sold: faker.datatype.number({ min: 0, max: 50 }),
  };
}

export const mockProducts = Array.from({ length: 30 }, generateMockProduct);

export type TCategory = {
  id: string;
  created_at: string;
  name: string;
  company: string;
  created_by: string;
  company_name: string;
  has_products: boolean;
  stock_count: number;
};

export type TLocation = {
  id: string;
  created_at: string;
  updated_at: string;
  location: string;
  default: boolean;
  customer: string;
  created_by: string;
};

export type TCustomer = {
  id: string;
  created_at: string;
  company: string;
  branch: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  status: "INACTIVE" | "ACTIVE";
  locations: TLocation[];
};

export type TNewCustomer = {
  company: string;
  branch: string;
  name: string;
  email: string;
  phone: string;
  address: string;
};

export interface ISubcategory {
  status: string
  status_code: number
  data: Data
  errors: null
}

export interface Data {
  message: string
  subcategories: Subcategory[]
  count: number
  total_subcategories: number
}

export interface Subcategory {
  id: string
  created_at: string
  name: string
  company: string
  created_by: string
  category: string
  category_name: string
}