'use client';

import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { format as formatDate, parseISO, subMonths } from 'date-fns';
import React from 'react';
import { DateRange } from 'react-day-picker';
import { useForm, useWatch } from 'react-hook-form';

import { Button, DataTable2 } from '@/components/ui';
import { useDebounce } from '@/hooks';
import { SmallSpinner } from '@/components/icons';
import { TOrderMgtBranchOrderHistory } from '../../types';
import { useGetOrderHistory } from '../../api';


const today = new Date();
const oneMonthAgo = subMonths(new Date(), 1);

interface OrdersCustomersTableProps {
  branchId: string;
}

export default function OrdersCustomersTable({
  branchId,
}: OrdersCustomersTableProps) {

  const columns: ColumnDef<TOrderMgtBranchOrderHistory>[] = [
    {
      accessorKey: 'id',
      header: 'S/N',
      cell: ({ row }) => {
        return <span>{row.index + 1}</span>;
      },
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const name = String(row.getValue('name'));
        return <span className="text-sm text-muted-foreground first-letter:capitalize">{name}</span>;
      },
    },

    {
      accessorKey: 'email',
      header: 'Email address',
      cell: ({ row }) => {
        const name = String(row.getValue('email'));
        return <span className="text-sm text-muted-foreground first-letter:lowercase">{name ?? ''}</span>;
      },
    },
    {
      accessorKey: 'current_stage',
      header: 'Current Stage',
      cell: ({ row }) => {
        const phone = String(row.getValue('current_stage'));
        return <span className="text-sm text-muted-foreground">{phone}</span>;
      },
    },
    {
      accessorKey: 'payment_status',
      header: 'Payment Status',
      cell: ({ row }) => {
        const phone = String(row.getValue('payment_status'));
        return <span className="text-sm text-muted-foreground">{phone}</span>;
      },
    },
    {
      accessorKey: 'last_updated',
      header: 'Last Updated',
      cell: ({ row }) => {
        const [day, month, year] = row.original.last_updated.split('-');
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        const formatted = formatDate(date, 'd/M/yyyy - hh:mma');
      
        return <span className="text-sm text-muted-foreground first-letter:">{formatted}</span>;
      },
    },
  ];

  const { control, register } = useForm<{
    searchFilter: string;
    dateFilter: DateRange;
  }>({
    defaultValues: {
      searchFilter: '',
      dateFilter: {
        from: oneMonthAgo,
        to: today,
      },
    },
  });

  const { searchFilter, dateFilter } = useWatch({ control });
  const debouncedSearchFilter = useDebounce(searchFilter, 500);

  const { from, to } = dateFilter || {};
  const startDate = from ? formatDate(from, 'yyyy-MM-dd') : '';
  const endDate = to ? formatDate(to, 'yyyy-MM-dd') : '';

  const [{ pageIndex, pageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: 1,
      pageSize: 100,
    });

  const fetchOptions = {
    pageIndex,
    pageSize,
    startDate,
    endDate,
    search: debouncedSearchFilter,
    branchId,
  };


  const { data: historyData, isLoading, isFetching } = useGetOrderHistory(fetchOptions)

  const { results: rows, count: total_order_count } = historyData || {};
  const pageCount = React.useMemo(
    () => (!!total_order_count ? Math.ceil(total_order_count / pageSize) : -1),
    [total_order_count, pageSize]
  );




  return (
    <div>
      <section className="mt-1 flex flex-wrap items-stretch justify-between gap-4 rounded-10 bg-background p-6 lg:py-5">
        <p className='text-sm text-[#0E0E2C] '>
          Customers:
          <span className='inline-block px-4 py-2 ml-1.5 rounded-md bg-main-bg '>
            {
              total_order_count ??
              <SmallSpinner color="white" />
            }
          </span>
        </p>

        <div className="hidden max-w-[17.1875rem] items-center gap-2.5 rounded-lg border border-[#d6d6d6]/50 px-4 transition duration-300 ease-in-out focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2">
          <input
            autoCapitalize="none"
            autoComplete="off"
            autoCorrect="off"
            className="grow px-0 py-3 text-sm ring-0 placeholder:text-[#556575] focus-visible:outline-none"
            id="team_name"
            placeholder="Search"
            type="text"
            {...register('searchFilter')}
          />
        </div>

      </section>

      <DataTable2
        columns={columns}
        isFetching={isFetching}
        isLoading={isLoading}
        pageCount={pageCount}
        pageIndex={pageIndex}
        pageSize={pageSize}
        rows={rows}
        setPagination={setPagination}
      />
    </div>
  );
}
