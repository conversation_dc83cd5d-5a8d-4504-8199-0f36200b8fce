import { TBranch } from '@/types/user';
import { convertToTitleCase } from '@/utils/strings';
import { format } from 'date-fns';
import React from 'react';
import { TCartItem, TCustomer } from '../types';
import { TSalesConfirmationResult } from '../api/patchConfirmSale';



export interface PrintReceiptFnProps {
    items: TSalesConfirmationResult;
    branchData: TBranch;
    iframeRef: React.RefObject<HTMLIFrameElement>
    paymentMethod: string;
    selectedCustomer?: TCustomer;
    batchId: string;
    salesConfirmationResult?: TSalesConfirmationResult

}

export const printReceipt = (props: PrintReceiptFnProps) => {
    const { items, branchData, iframeRef } = props
    const receiptContent = generateReceiptContent(props);

    if (iframeRef.current) {
        const iframe = iframeRef.current;
        const iframeWindow = iframe.contentWindow;
        if (iframeWindow) {
            iframeWindow.document.open();
            iframeWindow.document.write(receiptContent);
            iframeWindow.document.close();

            setTimeout(() => {
                try {
                    iframeWindow.focus();
                    iframeWindow.print();
                } catch (error) {
                    console.error('Printing failed:', error);
                    alert('Printing failed. Please check your printer connection and try again.');
                }
            }, 250);
        }
    }
};





const generateReceiptContent = ({ items, branchData, paymentMethod, selectedCustomer, batchId, salesConfirmationResult }: PrintReceiptFnProps): string => {

    const currentDate = new Date();
    const formattedDate = format(currentDate, 'dd/MM/yyyy');
    const formattedTime = format(currentDate, 'H:mm:ss aa');

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Receipt</title>
            <style>
                @media print {
                    @page {
                        margin: 0;
                        size: 80mm 297mm;
                    }
                    body {
                        font-family: DM Sans, sans-serif;
                        font-size: 12px;
                        line-height: 1.2;
                        width: 80mm;
                        margin: 0;
                        padding: 10px;
                        box-sizing: border-box;
                    }
                    .receipt-content {
                        width: 100%;
                    }
                    h1 {
                        font-size: 16px;
                        margin: 0;
                        text-align: center;
                    }
                    p {
                        margin: 5px 0;
                        text-align: center;
                    }
                    span {
                        font-size: 12px;
                    }
                    .small {
                        font-size: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 10px;
                        table-layout: fixed;
                    }
                    th, td {
                        text-align: left;
                        padding: 4px 0;
                        vertical-align: top;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    }
                    td{
                        font-size: 12px;
                    }
                    th:first-child, td:first-child {
                        width: 60%;
                    }
                    th:nth-child(2), td:nth-child(2) {
                        width: 15%;
                        padding-inline:3px;
                    }
                    th:last-child, td:last-child {
                        width: 25%;
                    }
                    .amount {
                        text-align: right;
                    }
                    .divider {
                        border-top: 1px dashed #000000B3;
                        margin: 16px 0;
                    }
                    .bold {
                        font-weight: semibold;
                    }
                    .info-row {
                        display: flex; 
                        justify-content: space-between;
                    }
                            .info-row-branch {
                        display: block; 
                        justify-content: space-between;
                    }
                }
            </style>
        </head>
        <body>
            <div class="receipt-content">
                <h1>${branchData.company}</h1>
                <p>${branchData.address}</p>
                <p>${branchData.phone ?? ""}</p>
                
                <div class="divider"></div>
                <p class="">${convertToTitleCase(paymentMethod)} Payment</p>
                <div class="divider"></div>
                
                <div class="info-row">
                <span>Order ID: #${batchId || Math.floor(1000 + Math.random() * 9000)}</span>
                <span>Date: ${formattedDate}</span>
                </div>
                <div class="info-row-branch">
                <div><span>Branch name: ${branchData.name || 'Staff'}</span></div>
               <div> <span>Phone no: ${branchData.phone || ''}</span></div>
               <div> <span>Time: ${formattedTime}</span></div>
                
                </div>
             
                <div class="divider"></div>

                <div class="info-row">
                <span>Name: ${selectedCustomer?.name || ''}</span>
                <span>Phone: ${selectedCustomer?.phone || ''}</span>
                </div>
                <div class="info-row">
                <span>Address: ${selectedCustomer?.address || ''}</span>
                </div>

                <div class="divider"></div>

                <table>
                    <thead>
                        <tr>
                            <th>Item name</th>
                            <th>Qty</th>
                            <th class="amount">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${items.items.map(item =>
        `
                            <tr>
                                <td>${item.item_description}</td>
                                <td>${item.quantity}</td>
                                <td class="amount">₦${(parseInt(item.amount) * item.quantity).toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                <div class="divider"></div>
                
                <div class="info-row">
                    <span>Subtotal:</span>
                    <span>₦${items.total.toFixed(2)}</span>
                </div>
   
               
                <div class="info-row">
                    <span>VAT:</span>
                    <span>₦${branchData.vat}</span>
                </div>
                <div class="info-row">
                <span>Discount:</span>
                <span>₦${salesConfirmationResult?.discount_value ?? ""}</span>
            </div>
            <div class="info-row">
                <span>Total:</span>
                <span>₦${salesConfirmationResult?.total ?? ""}</span>
            </div>
                
                <div class="divider"></div>
                
                <p>Thank you for your patronage!</p>
                <p>For feedback and enquiries contact</p>
                <p class="small">${branchData.company || '<EMAIL>'} / ${branchData.user || '00000000000'}</p>
            </div>
        </body>
        </html>
    `;
};