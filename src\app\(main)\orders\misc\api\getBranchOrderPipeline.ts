
import { useQuery } from '@tanstack/react-query';
import { OrderMgtPipelineDataResult } from '../types';
import { managementAxios } from '@/lib/axios';



export const getBranchOrdersPipeline = async (
  branchId: string
) => {
  const response = await managementAxios.get(
    `/orders/pipeline/details/${branchId}`  
  );

  return response.data as OrderMgtPipelineDataResult;
};

export const useGetBranchOrdersPipeline = (branchId: string) => {
  return useQuery<OrderMgtPipelineDataResult, Error>({
    queryKey: ['branch-order-details', branchId],
    queryFn: () => getBranchOrdersPipeline(branchId),
    enabled: !!branchId,
  });
};
