import { useState, useEffect, FC, Fragment } from "react";

import { cn } from "@/utils/classNames";
import { convertNumberToNaira } from "@/utils/currency";
import {
  Order,
  OrderDetail,
  OrderDetailData,
  Product,
} from "../../types/instore";
import { useCompanyStore } from "@/stores";
import { useGetBranchOrderDetails } from "../../api/getBranchOrders";
import { SmallSpinner } from "@/components/icons";
import { Button, ConfirmActionModal } from "@/components/ui";
import { Trash } from "lucide-react";
import { StatusEnum } from "./OrderMgtPipelineStageOrderCard";
import { useBooleanStateControl } from "@/hooks";

interface OrderMgtPipelineStageOrderDetailsProps {
  data: Order;
  order_details: OrderDetail;
  setOrder_details: (v: OrderDetail) => void;
  order_id: string;
  editedQuantities: Record<number, number>;
  setEditedQuantities: (v: any) => void;
  isDrawerOpen?: boolean;
}

const OrderMgtPipelineStageOrderDetails: FC<
  OrderMgtPipelineStageOrderDetailsProps
> = ({
  data,
  setEditedQuantities,
  editedQuantities,
  isDrawerOpen,
  order_details,
  setOrder_details,
}) => {
  const { company, branch } = useCompanyStore();
  const [isEditing, setIsEditing] = useState(false);
  const [itemToRemove, setItemToRemove] = useState<Product>();

  const {
    state: isConfirmRestoreModalOpen,
    setTrue: openConfirmRestoreModal,
    setFalse: closeConfirmRestoreModal,
  } = useBooleanStateControl(false);
  const { data: orderDetail, isLoading } = useGetBranchOrderDetails(
    branch,
    company,
    data.batch_id,
    isDrawerOpen
  );
  const isAvailable = orderDetail?.data.order_details;
  // const order_details = orderDetail?.data.order_details as OrderDetail;

  useEffect(() => {
    const initialQuantities: Record<number, number> = {};
    (order_details?.products ?? []).forEach((item, index) => {
      initialQuantities[index] = item.quantity;
    });
    setEditedQuantities(initialQuantities);
  }, [order_details, setEditedQuantities]);

  const handleQuantityChange = (index: number, value: number | string) => {
    setEditedQuantities((prev: any) => ({
      ...prev,
      [index]: parseInt(`${value}`) || 0,
    }));
  };

  const handleRemoveProduct = () => {
    const updatedProducts = order_details.products.filter(
      (v) => v.item_id !== itemToRemove?.item_id
    );
    setOrder_details({
      ...order_details,
      products: updatedProducts,
    });
    closeConfirmRestoreModal()
  };

  return (
    <div>
      {isLoading ? (
        <div className="grid place-content-center">
          <SmallSpinner />
        </div>
      ) : (
        <div>
          {isAvailable ? (
            <div className="flex flex-col gap-2.5">
              <section className="grid gap-4 bg-[#1C1C1C] px-4 py-6 rounded-xl md:grid-cols-2">
                <p className="text-sm text-[#7D8590]">
                  Order ID:
                  <span className="font-medium text-white">
                    #{orderDetail?.data.batch_id}
                  </span>
                </p>

                <p className="text-sm text-[#7D8590]">
                  Amount paid:
                  <span className="font-medium text-white">
                    {convertNumberToNaira(
                      (order_details as OrderDetail)?.total_cost
                    )}
                  </span>
                </p>

                <div className="flex flex-col gap-y-2">
                  <p className="text-xs">Status:</p>
                  <div className="flex items-center gap-2.5">
                    <span className={cn("rounded-md  py-1.5 text-[0.8rem]")}>
                      Payment:
                      <span
                        className={cn(
                          "ml-1",
                          !order_details?.paid
                            ? "text-[#EF4444]"
                            : "text-[#009444]"
                        )}
                      >
                        {order_details?.paid ? "Paid" : "Unpaid"}
                      </span>
                    </span>
                  </div>
                </div>

                <div className="flex flex-col gap-y-2">
                  <p className="text-xs">Stage:</p>
                  <span
                    className={cn(
                      "max-w-max rounded-md px-5 py-2 text-xs",
                      "!bg-[#5B391A] !text-[#FFAE63]"
                    )}
                  >
                    {order_details?.current_stage}
                  </span>
                </div>
              </section>

              <section className="grid gap-4 bg-[#1C1C1C] px-4 py-6 rounded-xl md:grid-cols-2">
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Customers name</p>
                  <p className="text-sm text-white">
                    {order_details?.customer_name ?? "N/A"}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Phone no</p>
                  <p className="text-sm text-white">
                    {order_details?.customer_phone ?? "N/A"}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Email</p>
                  <p className="text-sm text-white">
                    {order_details?.customer_email ?? "N/A"}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Channel</p>
                  <p className="text-sm text-white">
                    {order_details?.device ?? "N/A"}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Time</p>
                  <p className="text-sm text-white">
                    {new Date(
                      (order_details as OrderDetail)?.created_at
                    ).toLocaleTimeString("en-US", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="text-xs text-[#7D8590]">Date</p>
                  <p className="text-sm text-white">
                    {new Date(
                      (order_details as OrderDetail)?.created_at
                    ).toLocaleDateString("en-GB")}
                  </p>
                </div>
              </section>

              <section className="flex flex-col gap-2 bg-[#1C1C1C] px-4 py-6 rounded-xl">
                <div className="flex justify-between items-center">
                  <h6 className="mb-3 text-xs font-semibold text-white">
                    Order Details
                  </h6>
                  {/* <Button
                    className="bg-gray-500 text-white h-8"
                    onClick={
                      isEditing
                        ? () => setIsEditing(false)
                        : () => setIsEditing(true)
                    }
                  >
                    {isEditing ? "Cancel" : "Edit"}
                  </Button> */}
                </div>
                <div className="grid grid-cols-[1fr_0.5fr_0.3fr_0.5fr_0.3fr] justify-between gap-y-4 md:gap-4">
                  <p className="text-xs text-[#7D8590]">Item</p>
                  <p className="text-xs text-[#7D8590]">Status</p>
                  <p className="text-xs text-[#7D8590]">Quantity</p>
                  <p className="text-xs text-[#7D8590]">Price</p>
                  <p className="text-xs text-[#7D8590]">Action</p>

                  {order_details?.products.map((item, index) => (
                    <Fragment key={index}>
                      <div className="flex items-center gap-2 md:gap-4">
                        <p className="text-xs text-[#7D8590]">
                          {item.item_description}
                        </p>
                      </div>

                      <p className="text-sm font-semibold text-white self-center">
                        {item.status}
                      </p>

                      {isEditing ? (
                        <input
                          type="number"
                          min="0"
                          value={editedQuantities[index] || ""}
                          onChange={(e) =>
                            handleQuantityChange(index, e.target.value)
                          }
                          className="text-sm font-semibold text-white bg-[#2D2D2D] px-2 py-1 rounded w-16"
                        />
                      ) : (
                        <p className="text-sm font-semibold text-white self-center">
                          {item.quantity}
                        </p>
                      )}

                      <p className="text-sm font-semibold text-white self-center">
                        {convertNumberToNaira(item.quantity * item.amount)}
                      </p>
                      {order_details.current_stage === StatusEnum.NewOrder ? (
                        <p className="text-xs text-red-500 text-center">
                          <Trash
                            size={14}
                            onClick={() => {
                              setItemToRemove(item);
                              openConfirmRestoreModal();
                            }}
                            className="cursor-pointer"
                          />
                        </p>
                      ) : (
                        "."
                      )}
                    </Fragment>
                  ))}
                </div>
              </section>
            </div>
          ) : (
            <div>
              <h1>No Item Purchase</h1>
            </div>
          )}
        </div>
      )}

      <ConfirmActionModal
        closeModal={closeConfirmRestoreModal}
        confirmFunction={() => {
          handleRemoveProduct();
        }}
        // icon={<Rotate fill='white' height={44} width={44} />}
        isModalOpen={isConfirmRestoreModalOpen}
        title="Delete Order Item"
      >
        <p className="text-[#8C8CA1] text-sm font-normal">
          Are you sure you want to remove <strong className="text-white">{itemToRemove?.item_description}</strong>
        </p>
      </ConfirmActionModal>
    </div>
  );
};

export default OrderMgtPipelineStageOrderDetails;
