import { cn } from '@/utils/classNames';
import { faker } from '@faker-js/faker';
import React, { forwardRef, useMemo, useRef } from 'react';

interface ProductCategoriesItemProps {
  category: { name: string, id: string };
  label: string;
  type?: string;
  isActive: boolean;
  onCategorySelect: (category: { name: string, id: string }) => void;
}

const ProductCategoriesItem = forwardRef<HTMLDivElement, ProductCategoriesItemProps>(
  ({ category, label, isActive, onCategorySelect, type }, ref) => {

    const bgHue = useRef(faker.number.int({ min: 0, max: 360 })).current;
    const bgSaturation = useRef(faker.number.int({ min: 10, max: 30 })).current;
    const bgLightness = useRef(faker.number.int({ min: 15, max: 30 })).current;

    const backgroundColor = useMemo(() => {
      return `hsl(${bgHue}, ${bgSaturation}%, ${bgLightness}%)`;
    }, [bgHue, bgSaturation, bgLightness]);

    const textColor = useMemo(() => {
      const textSaturation = Math.min(bgSaturation + 60, 100);
      const textLightness = Math.min(bgLightness + 50, 90);
      return `hsl(${bgHue},${textSaturation}%,${textLightness}%)`;
    }, [bgHue, bgSaturation, bgLightness]);


    const focusClass = `focus:[border-color:${textColor}_!important;]`


    return (
      <div
        className={cn("border-[1.75px] border-transparent px-4 !flex !max-w-max whitespace-nowrap py-1.5 rounded-lg text-xs !outline-none  focus:scale-[1.2] transition-all duration-200 cursor-pointer",
          focusClass, isActive && 'border-white',
          category === null && '!bg-white !text-[#032282]',
          category === null && isActive && "border-[#032282]"
        )}
        ref={ref}
        style={{
          backgroundColor: type == "sub" ? "#000" : backgroundColor,
          color: type == "sub" ? "#fff" : textColor
        }}
        tabIndex={isActive ? 0 : -1}
        onClick={() => onCategorySelect(category)}
      >
        {label}
      </div>
    );
  }
);

export default ProductCategoriesItem;
ProductCategoriesItem.displayName = "ProductCategoriesItem"