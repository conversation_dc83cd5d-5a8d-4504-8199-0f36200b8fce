'use client'

import React, { createContext, useContext, useEffect, useReducer } from 'react';
import { useRouter } from 'next/navigation';
import { jwtDecode } from 'jwt-decode';

import { setAxiosDefaultToken, deleteAxiosDefaultToken, managementAxios } from '@/lib/axios';
import { tokenStorage } from '@/utils/auth';
import { useCompanyStore } from '@/stores';
import { TBranch } from '@/types/user';



type TShiftData = {
    ongoing_break: boolean,
    shift_ended: boolean,
    shift_started: boolean

}
interface AuthContextProps {
    isAuthenticated: boolean;
    isLoading: boolean;
    logout: () => void;
    shiftStatus: TShiftData;
    refetchShiftStatus: () => Promise<void>;
    isBreakVerified: boolean;
    startBreak: () => void;
    endBreak: () => void;
}

interface AuthState {
    isAuthenticated: boolean;
    isLoading: boolean;
    shiftStatus: TShiftData;
    isBreakVerified: boolean;
}

type AuthAction =
    | { type: 'LOGIN'; payload: TShiftData }
    | { type: 'LOGOUT' }
    | { type: 'SET_LOADING'; payload: boolean }
    | { type: 'START_BREAK' }
    | { type: 'END_BREAK' }


const AuthContext = createContext<AuthContextProps | undefined>(undefined);

const initialState: AuthState = {
    isAuthenticated: false,
    isLoading: true,
    shiftStatus: {
        ongoing_break: false,
        shift_ended: false,
        shift_started: false
    },
    isBreakVerified: false,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
    switch (action.type) {
        case 'LOGIN':
            return {
                ...state,
                isAuthenticated: true,
                shiftStatus: action.payload,
                isBreakVerified: true,
                isLoading: false
            };
        case 'LOGOUT':
            return initialState;
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload,
            };
        case 'START_BREAK':
            return {
                ...state,
                isBreakVerified: false
            }
        case 'END_BREAK':
            return {
                ...state,
                isBreakVerified: true
            }
      
        default:
            return state;
    }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);
    const router = useRouter();
    const { company, branch: branchId } = useCompanyStore()

    const refetchShiftStatus = async () => {
        const token = tokenStorage.getToken();

        try {
            const res = await managementAxios.get('/core/sales_user_shift_status', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            dispatch({ type: 'LOGIN', payload: res.data });
        } catch (error) {
            handleLogout();
        }
    };
   

    const handleLogout = () => {
        tokenStorage.clearToken();
        deleteAxiosDefaultToken();
        dispatch({ type: 'LOGOUT' });
        router.push('/login');
    };
    const handleStartBreak = () => {
        dispatch({ type: 'START_BREAK' });
        router.push('/login?break=true');
    };
    const handleEndBreak = () => {
        dispatch({ type: 'END_BREAK' });
    };


    useEffect(() => {
        const initializeAuth = async () => {
            const token = tokenStorage.getToken();

            if (token) {
                try {
                    const decodedToken = jwtDecode<{ exp: number }>(token);
                    const isTokenExpired = decodedToken.exp * 1000 < Date.now();

                    if (isTokenExpired) {
                        handleLogout();
                    } else {
                        setAxiosDefaultToken(token, managementAxios);
                        await refetchShiftStatus();
                    }
                } catch (error) {
                    handleLogout();
                }
            } else {
                handleLogout();
            }
        };

        initializeAuth();
    }, []);

    return (
        <AuthContext.Provider
            value={{
                isAuthenticated: state.isAuthenticated,
                isLoading: state.isLoading,
                logout: handleLogout,
                shiftStatus: state.shiftStatus,
                refetchShiftStatus,
                isBreakVerified: state.isBreakVerified,
                startBreak: handleStartBreak,
                endBreak: handleEndBreak,

            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextProps => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};