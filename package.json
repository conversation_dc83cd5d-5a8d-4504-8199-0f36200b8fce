{"name": "liberty-web-sales-pos", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@sentry/nextjs": "^8.33.1", "@tanstack/react-query": "^5.51.23", "@tanstack/react-table": "^8.20.1", "axios": "^1.7.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "framer-motion": "^11.5.4", "iconsax-react": "^0.0.8", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.427.0", "moment": "^2.30.1", "mqtt": "^5.10.1", "next": "^14.0.4", "react": "^18", "react-day-picker": "^8.7.1", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.4.1", "react-infinite-scroll-hook": "^5.0.1", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}