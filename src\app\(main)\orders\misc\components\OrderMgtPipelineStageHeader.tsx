import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MenubarContent, MenubarItem, MenubarMenu, MenubarSeparator, MenubarSub, MenubarSubContent, MenubarSubTrigger, MenubarTrigger, ToolTip } from '@/components/ui';
import { Elipsis, Info, MagnifyingLens } from '@/components/icons';
import { OrderMgtPipelineStageDetails } from '../types';
import { UseExportStageData } from '../api';
import JSZip from 'jszip';

interface OrderMgtPipelineStageHeaderProps {
    stage: OrderMgtPipelineStageDetails;
    pipeline_id: string;
    openSearchBox: () => void;
    filterUrl: string;
    searchText: string;
    selectedOrders: string[];
    openConfirmProgressRangeOrdersModal: () => void;
    openConfirmProgressSelectedOrdersModal: () => void;
    openConfirmCancelSelectedOrdersModal: () => void;
    openConfirmCancelRangeOrdersModal: () => void;
    startProgressIndex: number;
    endProgressIndex: number;
    setStartProgressIndex: (index: number) => void;
    setEndProgressIndex: (index: number) => void;
}
const OrderMgtPipelineStageHeader: React.FC<OrderMgtPipelineStageHeaderProps> = ({
    stage,
    openSearchBox,
    selectedOrders,
    startProgressIndex,
    endProgressIndex,
    setStartProgressIndex,
    setEndProgressIndex,
    openConfirmProgressSelectedOrdersModal,
    openConfirmProgressRangeOrdersModal,
    searchText,
    openConfirmCancelSelectedOrdersModal,
    openConfirmCancelRangeOrdersModal,
    pipeline_id
}) => {
    const [startExporting, setStartExporting] = useState(false);
    const { data: exportedData, isLoading } = UseExportStageData({
        pipeline_id,
        stage_id: stage.stage_id,
        enabled: startExporting,
    });


    const xlsxRef = useRef<HTMLAnchorElement | null>(null)


    useEffect(() => {
        if (exportedData && !isLoading) {
            const handleZipFile = async () => {
                try {
                    const arrayBuffer = await exportedData.arrayBuffer();
                    const zip = await JSZip.loadAsync(arrayBuffer);

                    const fileName = Object.keys(zip.files)[0];
                    const fileBlob = await zip.files[fileName].async('blob');

                    const url = URL.createObjectURL(fileBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `exported_stage_${stage.stage_name}.xlsx`;

                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('Error handling ZIP file:', error);
                } finally {
                    setStartExporting(false);
                }
            };

            handleZipFile();
        }
    }, [exportedData, isLoading, stage.stage_name]);



    return (
        <header className='sticky top-0 flex items-center justify-between gap-4 px-4 py-2 bg-black rounded-t-xl text-primary font-medium w-full'>
            <a className='hidden' href="" ref={xlsxRef}></a>

            <section className='flex items-center gap-4 text-sm'>
                <h2 className='flex items-center gap-1.5 text-sm truncate'>
                    {stage?.stage_name === 'New orders' ? 'New' : stage?.stage_name}
                    <span className='flex items-center justify-center px-2 py-1 text-white rounded-md text-xs font-bold'>
                        {stage.order_count}
                    </span>
                </h2>
            </section>
            <ToolTip content="Search stage" contentClass='text-xs font-normal'>
                <div className='relative flex items-center justify-center w-7 h-7' onClick={openSearchBox}>
                    <MagnifyingLens fill='#292D32' stroke='#292D32' strokeWidth={0.25} />
                    {searchText.trim() !== '' && (
                        <span className='absolute h-[0.45rem] w-[0.45rem] rounded-full -top-0 -right-0 bg-danger'></span>
                    )}
                </div>
            </ToolTip>
            <Menubar className="ml-auto">
                <MenubarMenu>
                    <ToolTip content="More" contentClass='text-xs font-normal' asChild>
                        <MenubarTrigger className='flex items-center justify-center !ml-auto h-7 w-7 rounded-full bg-white cursor-pointer'>
                            <Elipsis />
                        </MenubarTrigger>
                    </ToolTip>

                    <MenubarContent align="end">
                        <MenubarItem className='cursor-pointer' onClick={() => setStartExporting(true)}>
                            Export Stage Data
                        </MenubarItem>

                        <MenubarSub>
                            <MenubarSubTrigger>Progress Order</MenubarSubTrigger>
                            <MenubarSubContent>
                                <div className='flex flex-col gap-2 p-4'>
                                    <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                        Progress selected orders
                                        <span>
                                            ({selectedOrders.length})
                                        </span>
                                        <ToolTip
                                            content='Select the orders you want to progress by checking the checkbox on their cards.'
                                            contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                        >
                                            <Info />
                                        </ToolTip>
                                    </h3>
                                    <div className='flex items-center gap-2 justify-end'>
                                        <Button
                                            disabled={selectedOrders.length < 1}
                                            size='sm'
                                            onClick={openConfirmProgressSelectedOrdersModal}
                                        >
                                            Progress {selectedOrders.length} orders
                                        </Button>
                                    </div>
                                </div>
                                <MenubarSeparator className="border-b-2" />

                                <div className='p-4'>
                                    <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                        Progress orders range
                                        <ToolTip
                                            content='Input the range of orders you want to progress to the next stage. e.g. 1-5 to progress the first 5 orders to the next stage.'
                                            contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                        >
                                            <Info />
                                        </ToolTip>
                                    </h3>
                                    <section>
                                        <div className="grid grid-cols-2 gap-2">
                                            <div className="flex flex-col gap-1 my-1">
                                                <label className="!text-xs" htmlFor="start_move">From</label>
                                                <input
                                                    className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-xs !rounded-md !font-medium appearance-none"
                                                    id="start_move"
                                                    name="start_move"
                                                    type="number"
                                                    value={startProgressIndex}
                                                    onChange={(e) => setStartProgressIndex(parseInt(e.target.value))}
                                                />
                                            </div>
                                            <div className="flex flex-col gap-1 my-1">
                                                <label className="!text-xs" htmlFor="end_move">To</label>
                                                <input
                                                    className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-xs !rounded-md !font-medium appearance-none"
                                                    id="end_move"
                                                    name="end_move"
                                                    type="number"
                                                    value={endProgressIndex}
                                                    onChange={(e) => setEndProgressIndex(parseInt(e.target.value))}
                                                />
                                            </div>
                                        </div>
                                        <div className='flex items-center justify-end gap-2 mt-4'>
                                            <Button
                                                disabled={startProgressIndex < 1 || endProgressIndex < 1}
                                                size='sm'
                                                onClick={openConfirmProgressRangeOrdersModal}
                                            >
                                                Progress orders
                                            </Button>
                                        </div>
                                    </section>
                                </div>
                            </MenubarSubContent>
                        </MenubarSub>

                        <MenubarSub>
                            <MenubarSubTrigger>Cancel Order</MenubarSubTrigger>
                            <MenubarSubContent>
                                <div className='flex flex-col gap-2 p-4'>
                                    <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                        Cancel selected orders
                                        <span>
                                            ({selectedOrders.length})
                                        </span>
                                        <ToolTip
                                            content='Select the orders you want to cancel by checking the checkbox on their cards.'
                                            contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                        >
                                            <Info />
                                        </ToolTip>
                                    </h3>
                                    <div className='flex items-center gap-2 justify-end'>
                                        <Button
                                            disabled={selectedOrders.length < 1}
                                            size='sm'
                                            onClick={openConfirmCancelSelectedOrdersModal}
                                        >
                                            Cancel {selectedOrders.length} orders
                                        </Button>
                                    </div>
                                </div>
                                <MenubarSeparator className="border-b-2" />

                                <div className='p-4'>
                                    <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                        Cancel orders range
                                        <ToolTip
                                            content='Input the range of orders you want to cancel. e.g. 1-5 to cancel the first 5 orders.'
                                            contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                        >
                                            <Info />
                                        </ToolTip>
                                    </h3>
                                    <section>
                                        <div className="grid grid-cols-2 gap-2">
                                            <div className="flex flex-col gap-1 my-1">
                                                <label className="!text-xs" htmlFor="start_cancel">From</label>
                                                <input
                                                    className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-xs !rounded-md !font-medium"
                                                    id="start_cancel"
                                                    name="start_cancel"
                                                    type="number"
                                                    value={startProgressIndex}
                                                    onChange={(e) => setStartProgressIndex(parseInt(e.target.value))}
                                                />
                                            </div>
                                            <div className="flex flex-col gap-1 my-1">
                                                <label className="!text-xs" htmlFor="end_cancel">To</label>
                                                <input
                                                    className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-xs !rounded-md !font-medium"
                                                    id="end_cancel"
                                                    name="end_cancel"
                                                    type="number"
                                                    value={endProgressIndex}
                                                    onChange={(e) => setEndProgressIndex(parseInt(e.target.value))}
                                                />
                                            </div>
                                        </div>
                                        <div className='flex items-center justify-end gap-2 mt-4'>
                                            <Button
                                                disabled={startProgressIndex < 1 || endProgressIndex < 1}
                                                size='sm'
                                                onClick={openConfirmCancelRangeOrdersModal}
                                            >
                                                Cancel orders
                                            </Button>
                                        </div>
                                    </section>
                                </div>
                            </MenubarSubContent>
                        </MenubarSub>
                    </MenubarContent>
                </MenubarMenu>
            </Menubar>
        </header>
    )
}

export default OrderMgtPipelineStageHeader