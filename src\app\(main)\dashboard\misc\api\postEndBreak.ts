import { useAuth } from "@/contexts";
import { managementAxios } from "@/lib/axios";
import { useMutation } from "@tanstack/react-query";

const endBreak = async () => {
    const breakTime = await managementAxios.post(`/core/end_sales_shift_break/`);
    return breakTime.data
}

const UseEndBreak = () => {
  const { isAuthenticated, isLoading, shiftStatus, refetchShiftStatus } = useAuth();

    return useMutation({
        mutationFn: endBreak,
        onSuccess() {
            refetchShiftStatus()
        },
    })
}

export default UseEndBreak;