import { <PERSON><PERSON><PERSON><PERSON>, TwoTonedCopyIcon } from "@/components/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogFooter,
  DialogHeader,
  Input,
  Skeleton,
  RadioGroup,
} from "@/components/ui";
import { TBranch } from "@/types/user";
import { cn } from "@/utils/classNames";
import { useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, XIcon } from "lucide-react";
import React, { ChangeEvent, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useGetCustomers } from "../api/getCustomers";
import { useAddCustomer } from "../api/postAddCustomer";
import { useAddLocation } from "../api/postAddLocation";
import { TCartItem, TCustomer, TNewCustomer } from "../types";
import { convertNumberToNaira } from "@/utils/currency";
import { UseGetInstantAccount } from "../api";
import { useCompanyStore } from "@/stores";

interface Props {
  isModalOpen: boolean;
  closeModal: () => void;
  confirmFunction: () => void;
  isConfirming: boolean;
  amount: string;
  overallPrice?: number;
  items?: TCartItem[];
  branchData?: TBranch;
  paymentMethod?: string;
  company?: string;
  cashAmount?: number;
  otherAmount?: number;
  transferAmount?: number;
  isPartPayment: boolean;
  splitTransferMethod: string;
  setTransferAmount: (v: number) => void;
  setOtherAmount: (v: number) => void;
  setCashAmount: (v: number) => void;
  setPartPayment: (v: boolean) => void;
  setSplitTransferMethod: (v: string) => void;
  selectedCustomer?: TCustomer;
  setSelectedCustomer: (selectedCustomer: TCustomer | undefined) => void;
  batchId: string;
}

const CreditPaymentModal: React.FC<Props> = ({
  isModalOpen,
  closeModal,
  amount,
  confirmFunction,
  isConfirming,
  // items,
  branchData,
  paymentMethod,
  company,
  selectedCustomer,
  setSelectedCustomer,
  batchId,
  overallPrice,
  cashAmount,
  otherAmount,
  transferAmount,
  setCashAmount,
  setOtherAmount,
  setTransferAmount,
  setPartPayment,
  isPartPayment,
  setSplitTransferMethod,
  splitTransferMethod,
}) => {
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  // const total = items?.reduce(
  //   (acc, item) => acc + parseInt(item.selling_price) * item.quantity,
  //   0
  // );

  const { branch } = useCompanyStore();

  const [search, setSearch] = useState<string>("");
  const [isFocused, setIsFocused] = useState(false);
  const [addresses, setAddresses] = useState<string[]>([]);
  const [newCustomer, setNewCustomer] = useState<TNewCustomer>();

  const queryClient = useQueryClient();
  const [accountDetails, setAccountDetails] = useState({
    bank_name: "",
    bank_code: "",
    account_name: "",
    account_number: "",
  });
  //   React.useEffect(() => {
  //     if (isModalOpen) {
  //       buttonRef.current?.focus();
  //     }
  //   }, [isModalOpen]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape" /*|| event.key === "Backspace"*/) {
      closeModal();
    }
    if (event.key === "Enter") {
      event.preventDefault();
      confirmFunction();
    }
  };

  async function handlePaymentConfirmation() {}

  const { data: customersResponse, isLoading: isLoadingCustomers } =
    useGetCustomers(company as string, branchData?.id as string, search);
  const { mutate: addLocation, isPending: isAddingLocation } = useAddLocation();
  const { mutate: addNewCustomer, isPending: isAddingCustomer } =
    useAddCustomer();

  const customers = customersResponse?.data.customers;

  function handleSearch(value: string): void {
    setSearch(value);
  }

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setIsFocused(false);
    }, 300);
  };

  function removeAddress(index: number): void {
    setAddresses((prevArray) => prevArray.filter((_, i) => i !== index));
  }

  function handleNewCustomerChange(event: ChangeEvent<HTMLInputElement>): void {
    setNewCustomer({
      ...(newCustomer as TCustomer),
      [event.target.name]: event.target.value,
    });
  }

  function addAddress(index: number, customer: TCustomer): void {
    const location = addresses[index];
    addLocation(
      {
        customer: customer.id,
        location,
        default: false,
      },
      {
        onSuccess() {
          toast.success("Address added successfully");
          queryClient.invalidateQueries({
            queryKey: ["customers-list"],
          });
          setAddresses((prevArray) => prevArray.filter((_, i) => i !== index));
        },
      }
    );
  }

  function addCustomer(customer: TNewCustomer): void {
    addNewCustomer(customer, {
      onSuccess({ data }) {
        toast.success("Customer added successfully");
        queryClient.invalidateQueries({
          queryKey: ["customers-list"],
        });
        setSelectedCustomer(data || undefined);
        setNewCustomer(undefined);
        // Refetch the 'customers-list' query to get the updated data
        // queryClient
        //   .refetchQueries({ queryKey: ["customers-list"] })
        //   .then(() => {
        //     const customersList = queryClient.getQueryData<TCustomer[]>([
        //       "customers-list",
        //     ]);
        //     // Find the newly added customer by ID
        //     const newCustomer = customersList?.find((c) => c.id === data.id);
        //     setSelectedCustomer(newCustomer || undefined);
        //     setNewCustomer(undefined);
        //   });
      },
    });
  }
  const splitTransferOptions: {
    name: string;
    icon?: React.JSX.Element;
    value: string;
  }[] = useMemo(
    () => [
      { name: "Transfer", value: "SPLIT_TRANSFER" },
      {
        name: "Other transfers",
        value: "SPLIT_OTHER_TRANSFER",
      },
    ],
    []
  );

  const isButtonDisabled = () => {
    const { company, branch, name, address, email, phone } =
      newCustomer as TNewCustomer;
    return !company || !branch || !name || !address || (!email && !phone);
  };

  const { mutate: getInstantAccount, isPending: isGettingAccountDetails } =
    UseGetInstantAccount();

  // const vatRate = branchData.vat || 0;
  // const [totalPrice, setTotalPrice] = React.useState(
  //   active_cart.reduce(
  //     (sum, item) => sum + Number(item.selling_price) * item.quantity,
  //     0
  //   )
  // );
  const copyToClipboard = (string: string) => {
    navigator.clipboard.writeText(string);
    toast.success("Copied to clipboard");
  };

  React.useEffect(() => {
    if (
      paymentMethod === "TRANSFER" ||
      splitTransferMethod === "SPLIT_TRANSFER"
    ) {
      getInstantAccount(
        {
          company: company as string,
          branch,
          batch_id: batchId,
        },
        {
          onSuccess: (data) => {
            setAccountDetails(data.data);
          },
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [splitTransferMethod]);
  return (
    <Dialog modal={true} open={isModalOpen}>
      <DialogContent
        aria-label={"Record Credit"}
        className={cn(
          "rounded-[10px]",
          "my-6 bg-[#1C1C1C] !px-0 !pb-0 !pt-4 !max-w-[500px]"
        )}
        style={{
          width: "92.5%",
          minWidth: "300px",
        }}
        onPointerDownOutside={closeModal}
        onKeyDown={handleKeyDown}
      >
        {!isPartPayment && (
          <>
            <DialogHeader
              className={cn("max-sm:sticky top-0 z-10 px-6 md:px-8")}
            >
              <div className="flex items-center justify-between">
                <h5 className="text-base font-medium ">Record Credit</h5>
                <Button
                  className="bg-[#D9D9D91A] p-0 rounded-full h-9 w-9"
                  size={"sm"}
                  variant={"unstyled"}
                  onClick={closeModal}
                >
                  <XIcon size={20} />
                </Button>
              </div>
            </DialogHeader>
            <p className="px-8 py-4 text-white/80 text-sm">
              Purchase amount:
              <span className="ml-1 font-semibold">{amount}</span>
            </p>
            <p className="flex items-center text-sm text-[#A2A2A2] px-8">
              Search to select or add customer details
            </p>

            <div className="px-8 space-y-1">
              <Input
                className="h-[2.5rem] p-6"
                type="text"
                placeholder="Enter phone number to search or add"
                // value={inputValue}
                onChange={(e) => handleSearch(e.target.value)}
                onFocus={handleFocus}
                onBlur={handleBlur}
                rightIcon={
                  search &&
                  !customers?.length &&
                  !isLoadingCustomers && (
                    <Button
                      variant={"unstyled"}
                      className="mt-[-25%] mr-[-4]"
                      onClick={() => {
                        setSelectedCustomer(undefined);
                        setNewCustomer({
                          company: company as string,
                          branch: branchData?.id as string,
                          name: "",
                          email: "",
                          phone: search,
                          address: "",
                        });
                      }}
                    >
                      +
                    </Button>
                  )
                }
              />
              {customers?.length && isFocused ? (
                <div className="bg-[#313131] rounded-lg max-h-80 overflow-scroll absolute w-[calc(100%-4rem)] z-10">
                  {customers.map((customer) => (
                    <div
                      key={customer.id}
                      className="p-4 relative cursor-pointer"
                      onClick={() => {
                        setNewCustomer(undefined);
                        setSelectedCustomer(customer);
                      }}
                    >
                      <div>
                        {customer.phone} . {customer.name}
                      </div>
                      <div>{customer.address}</div>
                      <div className="absolute text-white top-3 right-5 text-xl">
                        +
                      </div>
                    </div>
                  ))}
                </div>
              ) : null}
              {newCustomer?.phone && (
                <div className="p-4 relative bg-[#D9D9D91A] rounded-lg mb-4 mt-5 space-y-2">
                  <div>
                    <div className="text-[#696969]">Name</div>
                    <Input
                      name="name"
                      onChange={handleNewCustomerChange}
                      value={newCustomer?.name}
                      type="text"
                    />
                  </div>
                  <div className="grid grid-cols-2 mb-3 gap-1">
                    <div>
                      <div className="text-[#696969]">Phone no</div>
                      <Input
                        name="phone"
                        onChange={handleNewCustomerChange}
                        value={newCustomer?.phone}
                        type="text"
                      />
                    </div>
                    <div>
                      <div className="text-[#696969]">Email</div>
                      <Input
                        name="email"
                        onChange={handleNewCustomerChange}
                        value={newCustomer?.email}
                        type="text"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="text-[#696969]">Address</div>
                    <Input
                      name="address"
                      onChange={handleNewCustomerChange}
                      value={newCustomer?.address}
                      type="text"
                    />
                  </div>
                  <Button
                    disabled={isButtonDisabled()}
                    // disabled={Object.values(newCustomer).some(value => !value)}
                    onClick={() => addCustomer(newCustomer)}
                  >
                    {isAddingCustomer ? <SmallSpinner /> : "Save"}
                  </Button>
                </div>
              )}
              {selectedCustomer ? (
                <div>
                  <div className="text-[#696969] mt-3 mb-1">
                    Tap edit icon to update details
                  </div>
                  <div className="p-4 relative bg-[#D9D9D91A] rounded-lg mb-4">
                    <div className="grid grid-cols-2 mb-3">
                      <div>
                        <div className="text-[#696969]">Phone no</div>
                        <div>{selectedCustomer.phone}</div>
                      </div>
                      <div>
                        <div className="text-[#696969]">Name</div>
                        <div>{selectedCustomer.name}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-[#696969]">Address</div>
                      <div>{selectedCustomer.address}</div>
                    </div>
                    <div
                      className="absolute top-3 right-5 text-xl"
                      onClick={() => handleFocus()}
                    >
                      <svg
                        width="18"
                        height="18"
                        viewBox="0 0 18 18"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M11.25 17.0625H6.75C2.6775 17.0625 0.9375 15.3225 0.9375 11.25V6.75C0.9375 2.6775 2.6775 0.9375 6.75 0.9375H8.25C8.5575 0.9375 8.8125 1.1925 8.8125 1.5C8.8125 1.8075 8.5575 2.0625 8.25 2.0625H6.75C3.2925 2.0625 2.0625 3.2925 2.0625 6.75V11.25C2.0625 14.7075 3.2925 15.9375 6.75 15.9375H11.25C14.7075 15.9375 15.9375 14.7075 15.9375 11.25V9.75C15.9375 9.4425 16.1925 9.1875 16.5 9.1875C16.8075 9.1875 17.0625 9.4425 17.0625 9.75V11.25C17.0625 15.3225 15.3225 17.0625 11.25 17.0625Z"
                          fill="white"
                        />
                        <path
                          d="M6.375 13.2674C5.9175 13.2674 5.4975 13.1024 5.19 12.8024C4.8225 12.4349 4.665 11.9024 4.7475 11.3399L5.07 9.08242C5.13 8.64742 5.415 8.08492 5.7225 7.77742L11.6325 1.86742C13.125 0.374922 14.64 0.374922 16.1325 1.86742C16.95 2.68492 17.3175 3.51742 17.2425 4.34992C17.175 5.02492 16.815 5.68492 16.1325 6.35992L10.2225 12.2699C9.915 12.5774 9.3525 12.8624 8.9175 12.9224L6.66 13.2449C6.5625 13.2674 6.465 13.2674 6.375 13.2674ZM12.4275 2.66242L6.5175 8.57242C6.375 8.71492 6.21 9.04492 6.18 9.23992L5.8575 11.4974C5.8275 11.7149 5.8725 11.8949 5.985 12.0074C6.0975 12.1199 6.2775 12.1649 6.495 12.1349L8.7525 11.8124C8.9475 11.7824 9.285 11.6174 9.42 11.4749L15.33 5.56492C15.8175 5.07742 16.0725 4.64242 16.11 4.23742C16.155 3.74992 15.9 3.23242 15.33 2.65492C14.13 1.45492 13.305 1.79242 12.4275 2.66242Z"
                          fill="white"
                        />
                        <path
                          d="M14.8877 7.3727C14.8352 7.3727 14.7827 7.3652 14.7377 7.3502C12.7652 6.7952 11.1977 5.2277 10.6427 3.2552C10.5602 2.9552 10.7327 2.6477 11.0327 2.5577C11.3327 2.4752 11.6402 2.6477 11.7227 2.9477C12.1727 4.5452 13.4402 5.8127 15.0377 6.2627C15.3377 6.3452 15.5102 6.6602 15.4277 6.9602C15.3602 7.2152 15.1352 7.3727 14.8877 7.3727Z"
                          fill="white"
                        />
                      </svg>
                    </div>
                  </div>
                  {/* {addresses.map((address, index) => (
                <div key={index} className="relative">
                  <textarea
                    className="w-full min-h-16 p-4 pt-9 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[#D9D9D91A]"
                    placeholder="Enter full address"
                    value={address}
                    onChange={(e) => {
                      setAddresses((prev) => {
                        const newArray = [...prev];
                        newArray[index] = e.target.value;
                        return newArray;
                      });
                    }}
                  ></textarea>
                  <Button
                    variant={"unstyled"}
                    className="absolute top-1 right-[56px] text-gray-500 hover:text-blue-500 cursor-pointer p-0 h-6"
                    disabled={!address}
                    onClick={() => addAddress(index, selectedCustomer)}
                  >
                    <SaveIcon />
                  </Button>
                  <Button
                    variant={"unstyled"}
                    className="absolute top-1 right-3 text-gray-500 hover:text-red-500 cursor-pointer p-0 h-6"
                    onClick={() => removeAddress(index)}
                  >
                    <Trash2Icon />
                  </Button>
                </div>
              ))} */}
                  {/* <Button
                className="p-0 h-full mt-4"
                variant={"ghost"}
                onClick={() => {
                  setAddresses([...addresses, ""]);
                }}
              >
                Tap to add new address <span className="text-xl ml-3">+</span>
              </Button> */}
                </div>
              ) : null}
            </div>
            <button
              type="button"
              className="px-8 py-4 text-white/80 text-sm flex cursor-pointer"
              onClick={() => {
                setPartPayment(true);
              }}
            >
              <span>Collect part payment</span>
              <svg
                width="26"
                height="20"
                viewBox="0 0 26 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M17.5222 12.3557C18.7806 11.0974 18.8225 9.08323 17.6481 7.77448L17.5222 7.64167L14.0894 4.40944C13.7639 4.084 13.2363 4.084 12.9108 4.40944C12.6104 4.70984 12.5873 5.18254 12.8415 5.50945L12.9108 5.58795L16.3437 8.82019C16.9603 9.4368 16.9928 10.4164 16.4411 11.0712L16.3437 11.1772L12.9108 14.4094C12.5854 14.7349 12.5854 15.2625 12.9108 15.588C13.2112 15.8884 13.6839 15.9115 14.0109 15.6573L14.0894 15.588L17.5222 12.3557Z"
                  fill="white"
                />
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M11.5222 12.3557C12.7806 11.0974 12.8225 9.08323 11.6481 7.77448L11.5222 7.64167L8.08936 4.40944C7.76392 4.084 7.23628 4.084 6.91085 4.40944C6.61044 4.70984 6.58733 5.18254 6.84152 5.50945L6.91085 5.58795L10.3437 8.82019C10.9603 9.4368 10.9928 10.4164 10.4411 11.0712L10.3437 11.1772L6.91085 14.4094C6.58541 14.7349 6.58541 15.2625 6.91085 15.588C7.21125 15.8884 7.68394 15.9115 8.01085 15.6573L8.08936 15.588L11.5222 12.3557Z"
                  fill="white"
                />
              </svg>
            </button>
            <DialogFooter className="flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8">
              <Button
                onClick={confirmFunction}
                ref={triggerRef}
                className="flex-col-reverse sm:flex-row sm:justify-center sm:space-x-2 flex items-center justify-end gap-4  w-full rounded-2xl p-5 py-6 md:px-8"
              >
                {isConfirming && <SmallSpinner color="black" />} Record Credit
              </Button>
            </DialogFooter>
          </>
        )}
        {isPartPayment && (
          <div className="px-4">
            <DialogHeader className={cn("max-sm:sticky top-0 z-10 ")}>
              <div className="flex items-center justify-between">
                <Button
                  className="flex gap-2 bg-[#D9D9D91A] text-white rounded-md py-2 px-3"
                  size={"sm"}
                  variant={"default"}
                  onClick={() => {
                    setPartPayment(false);
                  }}
                >
                  <ArrowLeft size={20} />
                  <span>Back</span>
                </Button>
                <Button
                  className="bg-[#D9D9D91A] p-0 rounded-full h-9 w-9"
                  size={"sm"}
                  variant={"unstyled"}
                  onClick={() => {
                    setPartPayment(false);
                  }}
                >
                  <XIcon size={20} />
                </Button>
              </div>
            </DialogHeader>
            <p className="py-4 font-bold">Split payment</p>

            <div className="text-gray-400 text-sm">
              Provide split details for
              <span className="text-white px-2">({amount})</span>
              below
            </div>
            <div className="my-1 space-y-1">
              <label className="text-sm">Cash</label>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setCashAmount(Number(e.target.value));
                }}
              />
            </div>
            <div className="my-1 space-y-1">
              <label className="block text-sm">Transfer</label>
              <span className="text-[11px] text-[#A2A2A2]">
                Enter amount and select transfer option
              </span>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setTransferAmount(Number(e.target.value));
                }}
              />
              <RadioGroup
                options={splitTransferOptions}
                name=""
                arrangement="row"
                onChange={(e) => {
                  setSplitTransferMethod(e);
                }}
                onclick={(e) => {
                  setSplitTransferMethod(e);
                }}
                value={splitTransferMethod}
                variant="offwhite"
                itemsContainerClass="!grid grid-cols-2 2xl:grid-cols-3 mb-4"
                className="!max-w-full"
              />
              {splitTransferMethod === "SPLIT_TRANSFER" && (
                <div className="">
                  <div className="flex flex-col justify-center items-center">
                    <p className="text-[0.825rem] text-[#A2A2A2]">Account No</p>

                    <p className="flex items-center justify-between">
                      {isGettingAccountDetails ? (
                        <Skeleton className="w-[150px] h-5 rounded-xl" />
                      ) : (
                        accountDetails.account_number
                      )}
                      <button
                        onClick={() =>
                          copyToClipboard(accountDetails.account_name)
                        }
                        disabled={isGettingAccountDetails}
                      >
                        <TwoTonedCopyIcon />
                      </button>
                    </p>
                  </div>
                  <div className="bg-black rounded-lg grid grid-cols-2 gap-2 py-1">
                    <div className="flex flex-col items-center space-y-1">
                      <p className="text-[10px] text-[#A2A2A2]">Account Name</p>
                      <p className="flex items-center justify-between text-sm text-center">
                        {isGettingAccountDetails ? (
                          <Skeleton className="w-[150px] h-5 rounded-xl" />
                        ) : (
                          accountDetails.account_name
                        )}
                      </p>
                    </div>
                    <div className="flex flex-col items-center space-y-1">
                      <p className="text-[10px] text-[#A2A2A2]">Bank Name</p>
                      <p className="flex items-center justify-between text-sm text-center">
                        {isGettingAccountDetails ? (
                          <Skeleton className="w-[150px] h-5 rounded-xl" />
                        ) : (
                          accountDetails.bank_name
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="my-1 space-y-1">
              <label className="block text-sm">Others</label>
              <Input
                type="number"
                placeholder="Enter Amount"
                onChange={(e) => {
                  setOtherAmount(Number(e.target.value));
                }}
              />
            </div>
            <div className="flex justify-between my-4">
              <div>
                <span className="text-gray-400">Total</span> :
                <span className="text-white font-medium ml-1">
                  {convertNumberToNaira(
                    (cashAmount ?? 0) +
                      (otherAmount ?? 0) +
                      (transferAmount ?? 0)
                  )}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Credit Bal</span> :
                <span className="text-white font-medium ml-1">
                  {convertNumberToNaira(
                    (overallPrice || 0) -
                      ((cashAmount ?? 0) +
                        (otherAmount ?? 0) +
                        (transferAmount ?? 0))
                  )}
                </span>
              </div>
            </div>

            <DialogFooter className="flex items-center justify-end gap-4  w-full rounded-2xl p-5 py-6 md:px-8">
              <Button
                disabled={Number(cashAmount) + Number(transferAmount) <= 0}
                onClick={confirmFunction}
                ref={triggerRef}
                className="flex-col-reverse sm:flex-row sm:justify-center sm:space-x-2 flex items-center justify-end gap-4 disabled:bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8"
              >
                {isConfirming && <SmallSpinner color="black" />} Record Credit
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
export default CreditPaymentModal;
