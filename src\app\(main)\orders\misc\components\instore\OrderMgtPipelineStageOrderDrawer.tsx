"use client";

import { useEffect, FC, useState } from "react";
// import toast from "react-hot-toast"

import {
  Drawer,
  Drawer<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui";
// import { getInitials } from "@/utils/strings"
import {
  OrderMgtPipelineStageDetails,
  TOrderMgtPipelineStageOrder,
} from "../../types";
// import { DoubleForward } from "@/icons/core"
import OrderMgtPipelineStageOrderDetails from "./OrderMgtPipelineStageOrderDetails";
import { XIcon } from "lucide-react";
// import { cn } from "@/utils/classNames";
// import { UseAcceptOrder, UseCancelOrder } from "../../api";
import { SmallSpinner } from "@/components/icons";
import { Order, OrderDetail } from "../../types/instore";
import { UsefulfilOrder } from "../../api/postAcceptOrder";
import { UseCancelOrderFullfilment } from "../../api/postCancelOrder";
import { useCompanyStore } from "@/stores";
import { useGetBranchOrderDetails } from "../../api/getBranchOrders";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { cn } from "@/utils/classNames";
import OrderMgtPipelineStageOrderTrail from "./OrderMgtPipelineStageOrderTrail";

interface props {
  isDrawerOpen: boolean;
  currentOrder: Order;
  closeDrawer: () => void;
  openDrawer: () => void;
}

const OrderDetailsDrawer: FC<props> = ({
  openDrawer,
  closeDrawer,
  currentOrder,
  isDrawerOpen,
}) => {
  const queryClient = useQueryClient();
  const [editedQuantities, setEditedQuantities] = useState<
    Record<number, number>
  >({});
  const [order_details, setOrder_details] = useState<OrderDetail>();

  const { company, branch, sales_user_role } = useCompanyStore();
  const { data: orderDetail, isLoading } = useGetBranchOrderDetails(
    branch,
    company,
    currentOrder.batch_id,
    isDrawerOpen
  );
  useEffect(() => {
    if (orderDetail?.data.order_details) {
      setOrder_details(orderDetail?.data.order_details);
    }
  }, [orderDetail?.data.order_details]);

  const removalTheme = "bg-[#EF4444] text-[#fff]"
  const reductionTheme = "bg-[#C78C00] text-[#000]"

  
  const { mutate: acceptOrderFn, isPending: isAcceptingOrder } =
    UsefulfilOrder();
  const { mutate: cancelOrderFn, isPending: isCancellingOrder } =
    UseCancelOrderFullfilment();
  const acceptOrder = () => {
    const updatedProducts = order_details?.products.map((item, index) => ({
      ...item,
      quantity: editedQuantities[index],
    }));

    // Call your API update function here
    const payload = {
      company,
      branch,
      batch_id: currentOrder.batch_id,
      sales: (updatedProducts ?? [])?.map((v) => ({
        category_id: v.category_id,
        item_id: v.item_id,
        quantity: v.quantity,
        amount: v.amount,
      })),
    };

    acceptOrderFn(payload, {
      onSuccess() {
        toast.success("Sales Fulfilled");
        queryClient.refetchQueries({ queryKey: ["branch-order"] });
        closeDrawer();
      },
    });
  };
  const cancelOrder = () => {
    const payload = {
      company,
      branch,
      batch_id: currentOrder.batch_id,
      action_type: "CANCELLED",
    };
    cancelOrderFn(payload, {
      onSuccess() {
        toast.success("Sales Cancelled Successfully");
        queryClient.refetchQueries({ queryKey: ["branch-order"] });
        closeDrawer();
      },
    });
  };

  const tabsArray = [
    {
      title: "Order details",
      value: "details",
      content: (
        <OrderMgtPipelineStageOrderDetails
          data={currentOrder}
          order_id={""}
          editedQuantities={editedQuantities}
          setEditedQuantities={setEditedQuantities}
          isDrawerOpen={isDrawerOpen}
          order_details={order_details as OrderDetail}
          setOrder_details={setOrder_details}
        />
      ),
    },
    {
      title: "Order trails",
      value: "trails",
      content: (
        <OrderMgtPipelineStageOrderTrail
          orderId={currentOrder.batch_id}
          closeDrawer={closeDrawer}
        />
      ),
    },
  ];

  return (
    <Drawer
      open={isDrawerOpen}
      onOpenChange={(open) => {
        if (!open) closeDrawer();
      }}
      direction="right"
      dismissible
    >
      {" "}
      <DrawerTrigger className="max-w-max px-4 py-1.5 m-0" asChild>
        <Button
          className={cn(
            "text-[0.7rem] px-6 bg-[#373737] h-8",
             currentOrder.reduction && reductionTheme, currentOrder.removal && removalTheme, 
          )}
          variant="secondary"
          size="sm"
          onClick={() => openDrawer()}
        >
          View
        </Button>
      </DrawerTrigger>
      <DrawerContent className="w-full md:w-[60%] lg:w-[50%] md:right-0 md:left-auto md:max-w-[650px] h-[90vh] md:h-screen !m-0 !p-0 bg-background text-white border-none rounded-l-2xl overflow-hidden">
        <div className="grow flex flex-col relative max-h-full overflow-y-hidden">
          <div className="w-full flex items-center justify-center bg-primary md:hidden">
            <div className="mx-auto mt-3 mb-1 h-1 w-[100px] rounded-full bg-white/60" />
          </div>

          <header className="sticky top-0 flex items-center justify-between w-full max-md:pt-1 px-5 md:px-8 gap-1.5 p-4 text-center sm:text-left border-b-[1px] border-[#C1C3C7]">
            <h3 className="font-medium text-white">Order Details</h3>
            <DrawerClose
              className="text-sm flex items-center justify-center bg-[#F2F5FF33] h-7 w-7 rounded-full font-medium text-white"
              onClick={() => closeDrawer()}
            >
              <XIcon size={15} />
            </DrawerClose>
          </header>

          <section className="grow flex flex-col overflow-y-scroll px-4 md:px-6">
            <header className="flex items-center gap-4 p-6 pb-4">
              {/* <Button
                className="bg-[#373737] h-10 rounded-md"
                variant="secondary"
              >
                Mark as read
              </Button>
              <Button
                className="border-[#787878] border-[0.4px] h-10 rounded-md"
                variant="outline"
              >
                Request payment
              </Button> */}
              <Button
                className="border-[#787878] border-[0.4px] h-10 rounded-md"
                variant="outline"
                onClick={cancelOrder}
              >
                Cancel order
                {isCancellingOrder && <SmallSpinner className="ml-2" />}
              </Button>
              {["PIPELINE", "ALL"].includes(sales_user_role) &&
                !["completed", "fulfilled"].includes(
                  currentOrder.order_stage.toLowerCase()
                ) && (
                  <Button
                    className="bg-white border-white border-[0.4px] h-10 rounded-md text-black"
                    variant="unstyled"
                    onClick={acceptOrder}
                  >
                    Fulfil order
                    {isAcceptingOrder && (
                      <SmallSpinner className="ml-2" color="#000" />
                    )}
                  </Button>
                )}
            </header>

            <div>
              <Tabs
                defaultValue={currentOrder.bar_alert ? "trails" : "details"}
              >
                <TabsList className="flex items-center justify-start gap-4 w-full border-b-[0.3px] border-[#C1C3C7] bg-transparent p-0 rounded-none">
                  {tabsArray.map((tab, index) => (
                    <TabsTrigger
                      className="!bg-transparent py-2.5 px-5 !border-b-2 border-transparent data-[state=active]:[border-style:solid_!important] data-[state=active]:border-white rounded-none"
                      key={index}
                      value={tab.value}
                    >
                      {tab.title}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {tabsArray.map((tab, index) => (
                  <TabsContent
                    className="p-4 px-0"
                    key={index}
                    value={tab.value}
                  >
                    {tab.content}
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </section>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default OrderDetailsDrawer;
