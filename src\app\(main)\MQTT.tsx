import React, { useState, useEffect } from 'react';
import mqtt, { MqttClient } from 'mqtt';

const MQTTComponent = () => {
  const [client, setClient] = useState<null | MqttClient>(null);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');
  const [messages, setMessages] = useState<{ topic: string; message: string }[]>([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const host = '**************'; 
      const port = '8083';
      const clientId = `mqtt_${Math.random().toString(16).slice(3)}`;
      const connectUrl = `ws://${host}:${port}/mqtt`;

      console.log(`Connecting to MQTT broker at ${connectUrl}`);

      const mqttClient = mqtt.connect(connectUrl, {
        clientId,
        clean: true,
        connectTimeout: 4000,
        reconnectPeriod: 1000,
      });

      mqttClient.on('connect', () => {
        setConnectionStatus('Connected');
        console.log('Connected to MQTT broker');
        mqttClient.subscribe('test/topic');
      });

      mqttClient.on('message', (topic, payload) => {
        const message = payload.toString();
        setMessages((prevMessages) => [...prevMessages, { topic, message }]);
        console.log(`Received message on topic ${topic}: ${message}`);
      });

      mqttClient.on('error', (err) => {
        console.error('MQTT Error:', err);
        setConnectionStatus(`Error: ${err.message}`);
      });

      mqttClient.on('close', () => {
        console.log('Connection closed');
        setConnectionStatus('Disconnected');
      });

      mqttClient.on('reconnect', () => {
        console.log('Reconnecting to MQTT broker');
      });

      mqttClient.on('offline', () => {
        console.log('MQTT client is offline');
      });

      setClient(mqttClient);

      return () => {
        if (mqttClient) {
          mqttClient.end();
        }
      };
    }
  }, []);

  const publishMessage = () => {
    if (client) {
      client.publish('test/topic', 'Hello from Next.js!');
      console.log('Published message: Hello from Next.js!');
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">MQTT in Next.js</h1>
      <p className="mb-2">Status: {connectionStatus}</p>
      <button
        onClick={publishMessage}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        disabled={connectionStatus !== 'Connected'}
      >
        Publish Message
      </button>
      <div className="mt-4">
        <h2 className="text-xl font-semibold mb-2">Received Messages:</h2>
        <ul className="list-disc pl-5">
          {messages.map((msg, index) => (
            <li key={index}>{`${msg.topic}: ${msg.message}`}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default MQTTComponent;
