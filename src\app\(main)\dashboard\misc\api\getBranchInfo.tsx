import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';

import { managementAxios } from '@/lib/axios';

const BranchDetailsResponseSchema = z.object({
  status: z.string(),
  status_code: z.number(),
  data: z.object({
    message: z.string(),
    branch: z.object({
      address: z.string(),
      company: z.string(),
      created_at: z.string(),
      id: z.string(),
      is_super_branch: z.boolean(),
      name: z.string(),
      updated_at: z.string(),
      user: z.string(),
      vat: z.number(),
      stock_value: z.number().nullable(),
      sales_value: z.number().nullable(),
      expected_profit: z.number().nullable(),
      sell_without_inventory: z.boolean(),

      opening_stock: z.object({
        date: z.string().nullable(),
        time: z.string().nullable(),
        total_stocks: z.number().nullable(),
      }),

      closing_stock: z.object({
        date: z.string().nullable(),
        time: z.string().nullable(),
        total_stocks: z.number().nullable(),
      }),

      members: z.array(
        z.object({
          name: z.string(),
          email: z.string(),
        })
      ),
      account_details: z.object({
        account_name: z.string(),
        account_number: z.string(),
        bank_name: z.string(),
      }),
    }),
  }),
});

type BranchDetailsResponse = z.infer<typeof BranchDetailsResponseSchema>;

export const getBranchDetails = async (
  params: { queryKey: any; signal?: AbortSignal; meta?: Record<string, unknown> | undefined; pageParam?: unknown; direction?: unknown; }//: QueryFunctionContext<string[], unknown>
) => {
  const [companyId, branchId] = params.queryKey;
  const response = await managementAxios.get(
    `/api/v1/stock/branches/?company=${companyId}&branch=${branchId}`
  );
  return response.data as BranchDetailsResponse;
};

export const useGetBranchDetails = (companyId: string, branchId: string) => {
  return useQuery({
    queryKey: [companyId, branchId, 'branch-details'],
    queryFn: params => getBranchDetails(params),
    enabled: !!companyId && !!branchId,
  });
};
