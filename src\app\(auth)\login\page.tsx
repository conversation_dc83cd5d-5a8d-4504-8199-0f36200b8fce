'use client'
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { z } from "zod";

import { PayboxLogo, SmallSpinner } from '@/components/icons';
import { Button, Input } from '@/components/ui';
import { managementAxios, setAxiosDefaultToken } from '@/lib/axios';
import { useCompanyStore } from '@/stores';
import { tokenStorage } from '@/utils/auth';
import { zodResolver } from '@hookform/resolvers/zod';

import { UseEndBreak } from '@/app/(main)/dashboard/misc/api';
import { useAuth } from '@/contexts';
import { TBranch } from '@/types/user';
import { cn } from '@/utils/classNames';
import { UseLoginMutation } from '../misc/api';



const LoginSchema = z.object({
    username: z.string(),
    password: z.string().min(6).max(6)
})

export type loginDataType = z.infer<typeof LoginSchema>

const UnauthenticatedUserHomepage = () => {

    const {
        register, control, handleSubmit, setValue, watch, setError, clearErrors, formState: { isValid, errors }, reset
    } = useForm<loginDataType>({
        resolver: zodResolver(LoginSchema),
    });
    const { setCompanyInfo } = useCompanyStore()

    const router = useRouter()
    const { mutate, isPending: isLoggingIn } = UseLoginMutation()
    const { refetchShiftStatus, shiftStatus, endBreak } = useAuth();
    const { mutateAsync: endBreakMutation, isPending: isEndingShift } = UseEndBreak()


    const SubmitForm = async (submitData: loginDataType) => {
        mutate(submitData, {
            onSuccess: async (data, variable) => {
                if (data.status_code === "102" && data.message === "User should reset password") {
                    if (window && typeof window !== 'undefined') {
                        localStorage.setItem('newUser', JSON.stringify(variable));
                    }
                    router.push('/reset-passcode');
                    return
                }
                const { company, branch, access, username, first_name, last_name, sales_user_role } = data
                console.log(last_name, sales_user_role );
                
                tokenStorage.setToken(access);
                setAxiosDefaultToken(access, managementAxios);

                const res = await managementAxios.get(`/api/v1/stock/branches/?company=${company}`, {
                    headers: {
                        Authorization: `Bearer ${access}`
                    }
                });
                const branchData = res.data?.data?.branches.find((branchData: TBranch) => branchData.id === branch) as TBranch;
                setCompanyInfo({ company, branch, branchData, first_name, last_name, username, sales_user_role })
                refetchShiftStatus()

                if (shiftStatus.ongoing_break) {
                    await endBreakMutation()
                    await endBreak()
                    return
                } else if (shiftStatus.shift_started && !shiftStatus.shift_ended) {
                    router.push('/dashboard')
                    return
                }
                router.push('/onboard/shift')

            },
            onError(error) {
                if (error.response?.status === 401 || error.response?.status === 400) {
                    // const responseData = error.response.data;
                    // const status_code = responseData.status_code[0]
                    // const message = responseData.message[0]

                    if (error?.response?.data.message == "Invalid username/password") {
                        setError('password', {
                            type: 'manual',
                            message: 'Invalid username/password'
                        })
                        setError('username', {
                            type: 'manual',
                            message: 'Invalid username/password'
                        })
                    }

                }
                if (error.response?.status === 400) {
                    const responseData = error.response.data;
                    const status_code = responseData.status_code[0]
                    const message = responseData.message[0]

                    if (status_code == "105" && message == "User does not belong to a company") {
                        setError('username', {
                            type: 'manual',
                            message: 'User does not belong to a company'
                        })
                    }
                }
            },
        })
    }



    return (
        <div className='size-full flex items-center justify-center'>
            <article className='flex flex-col items-center justify-center gap-4 w-full max-w-[520px] min-h-[320px] mx-auto p-4 py-16 rounded-xl bg-[#1c1c1c96]'>
                <div className={cn('flex flex-col items-center gap-2')}>
                    <PayboxLogo width={70} height={70} />
                    <h1 className="flex flex-col font-clash font-bold text-[1.5rem]">
                        Paybox360
                    </h1>
                </div>

                <form action="" className='flex flex-col gap-4 w-full md:w-[80%] my-12' onSubmit={handleSubmit(SubmitForm)}>
                    <Input type='text' placeholder='Username' className='bg-transparent login-no-chrome-autofill-bg' {...register('username')} />
                    <Input
                        type='password'
                        placeholder='Enter Password'
                        className='bg-transparent login-no-chrome-autofill-bg'  {...register('password')}
                        hasError={!!errors.password}
                        errorMessage={errors.password?.message}
                    />

                    <Button className='mt-12 w-full'>
                        Continue
                        {
                            (isLoggingIn || isEndingShift) && <SmallSpinner color="black" className='ml-2' />
                        }
                    </Button>
                </form>
            </article>
        </div>
    )
}

export default UnauthenticatedUserHomepage