'use client'


import { ModalRouteConditionalRenderer } from '@/components/layout';
import { useAuth } from '@/contexts';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';




export default function RootLayout({
    children,
    modal

}: {
    children: React.ReactNode;
    modal: React.ReactNode
}) {
    const router = useRouter();
    const { isAuthenticated, isLoading, shiftStatus, refetchShiftStatus, isBreakVerified } = useAuth();
    useEffect(() => {
        if (!isLoading && shiftStatus.ongoing_break && !isBreakVerified) {
            router.replace('/login?break=true');
        }
    }, [isLoading, isAuthenticated, shiftStatus, refetchShiftStatus, isBreakVerified, router]);

    return (
        <>
            {children}
            <ModalRouteConditionalRenderer pathsWithModal={['/onboard/shift', '/onboard/break']}>
                {modal}
            </ModalRouteConditionalRenderer>
        </>
    );
}
