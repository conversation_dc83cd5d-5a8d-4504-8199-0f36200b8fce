import React, { useEffect, useState } from "react";
import { Bag } from 'iconsax-react'

import { cn } from '@/utils/classNames'
import { CloseSquare } from "@/components/icons";
import { useCartStore } from "@/stores";

import { TCartItem } from "../types";
import { Input } from "@/components/ui";

interface CartProps {
    items: TCartItem[];
    onUpdateQuantity: (id: string, change: number) => void;
    onRemoveItem: (id: string) => void;
}

export const Cart = React.forwardRef<HTMLDivElement, CartProps>((props, ref) => {
    const { items, onUpdateQuantity, onRemoveItem } = props;
    const [focusedIndex, setFocusedIndex] = useState(0);
    const { add_item, remove_item, delete_item } = useCartStore()

    const handleKeyDown = (event: React.KeyboardEvent<HTMLElement>) => {
        event.preventDefault()
        if (event.key === 'ArrowDown') {
            setFocusedIndex(prev => Math.min(prev + 1, items.length - 1));
        } else if (event.key === 'ArrowUp') {
            setFocusedIndex(prev => Math.max(prev - 1, 0));
        } else if (event.key === 'Enter' && focusedIndex >= 0) {
            add_item(items[focusedIndex])
        } else if (event.key === '+' && focusedIndex >= 0) {
            onUpdateQuantity(items[focusedIndex]?.item_id, 1)
        } else if (event.key === '-' && focusedIndex >= 0) {
            remove_item(items[focusedIndex]?.item_id)
            if (focusedIndex === items.length) {
                setFocusedIndex(focusedIndex - 1)
            }
        } else if (event.key === 'Delete') {
            delete_item(items[focusedIndex]?.item_id)
            if (focusedIndex === items.length - 1) {
                setFocusedIndex(focusedIndex - 1)
            }
        } else if (event.key === 'Escape') {
            setFocusedIndex(-1)
        }
    };

    useEffect(() => {
        if (items.length === 0) {
            setFocusedIndex(-1)
        }
    }, [items])



    return (
        <div className="grow min-h-max shrink-0 bg-[#1C1C1C] divide-y-[1.2px] divide-[#262729] p-4 rounded-xl border-2 border-transparent focus:border-blue-600 !outline-none overflow-y-scroll"
            ref={ref}
            onKeyDown={handleKeyDown}
            tabIndex={0}
        >
            {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-[#d8d8dfd1] text-sm">
                    <Bag className="w-12 h-12 rounded-full mb-4" />
                    Checkout list will appear here
                </div>
            ) : (
                <>
                    {items.map((item, index) => (
                        <article
                            key={item.item_id}
                            tabIndex={index === focusedIndex ? 0 : -1}
                            className={cn("grid grid-cols-[1fr,0.5fr,1fr] items-center justify-between gap-5 py-3 px-2.5 text-sm",
                                {
                                    'bg-[#333]': index === focusedIndex
                                })
                            }
                        >
                            <p className="truncate">
                                {item.item}
                            </p>

                            <div className="flex items-center gap-1.5 justify-self-center">
                                <button
                                    onClick={() => remove_item(item.item_id)}
                                    aria-label={`Decrease quantity of ${item.item}`}
                                    className="flex items-center justify-center w-6 h-5 p-0 bg-[#F9F9F933] font-light text-sm rounded-md"
                                >
                                    -
                                </button>

                                <input
                                    className="outline-none !h-8 text-center appearance-none max-w-[50px]"
                                    value={item.quantity}
                                    onChange={(e) => add_item({ ...item, quantity: Number(e.target.value) })}
                                    type="number"
                                    min="1"
                                />

                                <button
                                    onClick={() => add_item(item)}
                                    aria-label={`Increase quantity of ${item.item}`}
                                    className="flex items-center justify-center w-6 h-5 p-0 bg-[#F9F9F933] font-light text-sm rounded-md"
                                >
                                    +
                                </button>
                            </div>

                            <div className="flex items-center gap-2 justify-self-end">
                                <p className="">
                                    N{Number(item.selling_price) * item.quantity}
                                </p>

                                <button onClick={() => onRemoveItem(item.item_id)} aria-label={`Remove ${item.item} from cart`}>
                                    <CloseSquare />
                                </button>
                            </div>
                        </article>
                    ))}
                </>
            )}
        </div>
    );
});

Cart.displayName = 'Cart';