import React, { useEffect, useImperativeHandle, useMemo, useRef } from "react";

import { cn } from '@/utils/classNames';
import { faker } from "@faker-js/faker";

import { convertNumberToNaira } from "@/utils/currency";
import toast from "react-hot-toast";
import { TCartItem, TProduct } from "../types";
import { useCompanyStore } from "@/stores";

interface ProductItemProps {
    product: TProduct;
    onAddToCart: (product: TProduct) => void;
    onRemoveFromCart: (id: string) => void;
    isFocused: boolean;
    cartItems: TCartItem[];
    sell_without_inventory?: boolean;
}

export const ProductItemCard = React.forwardRef<HTMLElement, ProductItemProps>(
    ({ product, onAddToCart, onRemoveFromCart, isFocused, cartItems, sell_without_inventory }, ref) => {
        const itemRef = useRef<HTMLElement>(null);
        const cartItem = cartItems.find(item => item.item_id === product.item_id) || {} as TCartItem;
        const {  branchData } =
        useCompanyStore();
        useImperativeHandle(ref, () => itemRef.current!);

        useEffect(() => {
            if (isFocused && itemRef.current) {
                itemRef.current.focus();
            }
        }, [isFocused]);

        const handleKeyDown = (event: React.KeyboardEvent<HTMLLIElement>) => {
            if (event.key === '+') {
                if (!sell_without_inventory && ((cartItem.quantity || 0) >= product.quantity)) {
                    toast.error(`insufficient stock for ${product.item}`)
                } else {
                    onAddToCart(product);
                }
            } else if (event.key === '-') {
                onRemoveFromCart(product.item_id);
            }
        };

        const hue = useMemo(() => faker.number.int({ min: 0, max: 360 }), []);
        const saturation = useMemo(() => faker.number.int({ min: 50, max: 100 }), []);
        const lightness = useMemo(() => faker.number.int({ min: 10, max: 40 }), []);
        const bg = useMemo(
            () => `hsl(${hue}, ${saturation}%, ${lightness}%)`,
            [hue, saturation, lightness]
        );

        return (
            <article
                ref={itemRef}
                tabIndex={isFocused ? 0 : -1}
                onKeyDown={handleKeyDown}
                className={cn("flex flex-col gap-1 p-3 rounded-xl aspect-[5/3] max-h-[150px] focus:outline-none outline-none border-2 border-transparent cursor-pointer", { 'border-white': isFocused })}
                style={{ backgroundColor: bg }}
                // what is this
                onClick={() => {
                    if (!sell_without_inventory && ((cartItem.quantity || 0) >= product.quantity)) {
                        toast.error(`insufficient stock for ${product.item}`)
                    } else {
                        onAddToCart({...product, branch: branchData.id});
                    }
                }}
            >
                <div className="flex items-center justify-between gap-3">
                    <p className="truncate font-medium text-[0.875rem]">
                        {product.item}
                    </p>
                    <p className="truncate font-medium text-[0.875rem] flex items-center gap-[2px]">
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.25004 9.47982H3.75004C1.48754 9.47982 0.520874 8.51315 0.520874 6.25065V3.75065C0.520874 1.48815 1.48754 0.521484 3.75004 0.521484H6.25004C8.51254 0.521484 9.47921 1.48815 9.47921 3.75065V6.25065C9.47921 8.51315 8.51254 9.47982 6.25004 9.47982ZM3.75004 1.14648C1.82921 1.14648 1.14587 1.82982 1.14587 3.75065V6.25065C1.14587 8.17149 1.82921 8.85482 3.75004 8.85482H6.25004C8.17088 8.85482 8.85421 8.17149 8.85421 6.25065V3.75065C8.85421 1.82982 8.17088 1.14648 6.25004 1.14648H3.75004Z" fill="white" />
                            <path d="M5.00001 5.44996C4.94584 5.44996 4.89167 5.43746 4.84167 5.40829L2.63334 4.13329C2.48334 4.04579 2.43334 3.85412 2.52084 3.70829C2.60834 3.55829 2.80001 3.50829 2.94584 3.59579L4.99584 4.78329L7.03334 3.60412C7.18334 3.51662 7.37501 3.57079 7.45834 3.71662C7.54167 3.86246 7.49168 4.05829 7.34584 4.14163L5.15417 5.40829C5.10834 5.43329 5.05417 5.44996 5.00001 5.44996Z" fill="white" />
                            <path d="M5 7.71588C4.82917 7.71588 4.6875 7.57422 4.6875 7.40338V5.13672C4.6875 4.96589 4.82917 4.82422 5 4.82422C5.17083 4.82422 5.3125 4.96589 5.3125 5.13672V7.40338C5.3125 7.57422 5.17083 7.71588 5 7.71588Z" fill="white" />
                            <path d="M5.0001 7.81185C4.75843 7.81185 4.52094 7.75768 4.32927 7.65352L2.99594 6.91185C2.59594 6.69101 2.2876 6.16185 2.2876 5.70352V4.29102C2.2876 3.83685 2.6001 3.30768 2.99594 3.08268L4.32927 2.34102C4.71261 2.12852 5.2876 2.12852 5.67093 2.34102L7.00427 3.08268C7.40427 3.30352 7.7126 3.83268 7.7126 4.29102V5.70352C7.7126 6.15768 7.4001 6.68685 7.00427 6.91185L5.67093 7.65352C5.47926 7.76185 5.24177 7.81185 5.0001 7.81185ZM5.0001 2.81185C4.8626 2.81185 4.72927 2.83685 4.63343 2.89102L3.30011 3.63268C3.09594 3.74518 2.9126 4.06185 2.9126 4.29102V5.70352C2.9126 5.93685 3.09594 6.24935 3.30011 6.36185L4.63343 7.10352C4.8251 7.21185 5.1751 7.21185 5.36677 7.10352L6.7001 6.36185C6.90426 6.24935 7.0876 5.93268 7.0876 5.70352V4.29102C7.0876 4.05768 6.90426 3.74518 6.7001 3.63268L5.36677 2.89102C5.27094 2.83685 5.1376 2.81185 5.0001 2.81185Z" fill="white" />
                        </svg>
                        {product.quantity}
                    </p>
                </div>
                {product.category && (
                    <p className="text-[0.625rem] truncate">{product.category}</p>
                )}
                <p className="text-[0.8rem] font-bold">
                    {convertNumberToNaira(parseInt(product.selling_price))}
                </p>
                <footer className="grid grid-cols-[1fr,0.2fr,1fr] items-center gap-2 mt-2">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onRemoveFromCart(product.item_id);
                        }}
                        aria-label={`Remove ${product.item} from cart`}
                        className="flex items-center justify-center w-full h-full py-1 font-light text-xl bg-white/20 rounded-md"
                    >
                        -
                    </button>
                    <p className="text-sm">
                        {cartItem.quantity || 0}
                    </p>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            if (!sell_without_inventory && ((cartItem.quantity || 0) >= product.quantity)) {
                                toast.error(`insufficient stock for ${product.item}`)
                            } else {
                                onAddToCart(product);
                            }
                        }}
                        aria-label={`Add ${product.item} to cart`}
                        className="flex items-center justify-center w-full h-full py-1 font-light text-xl bg-white/20 rounded-md"
                    >
                        +
                    </button>
                </footer>
            </article>
        );
    }
);

ProductItemCard.displayName = "ProductItemCard";
