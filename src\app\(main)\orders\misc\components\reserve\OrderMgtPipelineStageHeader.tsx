// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ubarContent, <PERSON>ubar<PERSON><PERSON>, MenubarMenu, MenubarSeparator, MenubarSub, MenubarSub<PERSON>ontent, MenubarSubTrigger, <PERSON>ubar<PERSON>rigger, ToolTip } from '@/components/core'
import { <PERSON><PERSON><PERSON>, MenubarContent, MenubarItem, MenubarMenu, MenubarTrigger, ToolTip } from '@/components/ui'
// import { Elipsis, MagnifyingLens } from '@/icons/core'
// import FilterIcon from '@/icons/core/FilterIcon'
import React from 'react'
import { OrderMgtPipelineStageDetails } from '../../types';
import { Ellipsis, EllipsisVertical, FilterIcon, Search } from 'lucide-react';


interface OrderMgtPipelineStageHeaderProps {
    stage: OrderMgtPipelineStageDetails;
    openSearchBox: () => void;
    openPreviewModal: () => void;
    filterUrl: string;
    searchText: string;
    openConfirmProgressModal: () => void;



}
const OrderMgtPipelineStageHeader: React.FC<OrderMgtPipelineStageHeaderProps> = ({ stage, openSearchBox, openPreviewModal, filterUrl, searchText }) => {


    return (
        <header className='sticky top-0 flex items-center justify-between gap-4 px-4 py-2 bg-black rounded-t-xl text-primary font-medium w-full'>
            <section className='flex items-center gap-4 text-sm'>
                <h2 className='flex items-center gap-1.5 text-sm truncate'>
                    {
                        stage?.stage_name === "New orders" ? "New" : stage?.stage_name
                    }
                    <span className='flex items-center justify-center px-2 py-1 bg-[#1C1C1C] rounded-md text-xs font-bold'>{stage.order_count}</span>
                </h2>
                {/* <div>
                    {
                        (stage_name === "New candidate" || order == 0) &&
                        <h2 className='flex items-center gap-1.5 text-sm '>
                            <span>Qualified</span>
                            <span className='flex items-center justify-center px-2 py-1 bg-white rounded-md text-xs font-bold'>
                                {qualifiedCandidates}
                            </span>
                        </h2>
                    }
                </div> */}

            </section>

            <section className='flex items-center gap-3'>
                <ToolTip content="Filter applicants" contentClass='text-xs font-normal'>
                    <div className='relative flex items-center justify-center w-7 h-7 rounded-full bg-[#272727] ' onClick={openPreviewModal}>
                        <FilterIcon size={15} />
                        {
                            (filterUrl && filterUrl.trim() !== "") &&
                            <span className='absolute h-[0.45rem] w-[0.45rem] rounded-full -top-0 -right-0 bg-danger'></span>
                        }
                    </div>
                </ToolTip>

                <ToolTip content="Search stage" contentClass='text-xs font-normal'>
                    <div className='relative flex items-center justify-center w-7 h-7 rounded-full bg-[#272727] ' onClick={openSearchBox}>
                        <Search size={15} />
                        {
                            searchText && searchText.trim() !== "" &&
                            <span className='absolute h-[0.45rem] w-[0.45rem] rounded-full -top-0 -right-0 bg-danger'></span>
                        }
                    </div>
                </ToolTip>




                <Menubar className="ml-auto border-0" >
                    <MenubarMenu>
                        <MenubarTrigger className='flex items-center justify-center !ml-auto p-0 h-7 w-7 rounded-full bg-[#272727] border-none cursor-pointer'>
                            <ToolTip content="More" contentClass='text-xs font-normal' asChild>
                                <EllipsisVertical size={15} color="white" />
                            </ToolTip>
                        </MenubarTrigger>

                        <MenubarContent align="end">
                            <MenubarItem>
                                Edit stage
                            </MenubarItem>
                            <MenubarItem>
                                Export stage data
                            </MenubarItem>

                            {/* <MenubarSub>
                                <MenubarSubTrigger>Rescore candidates</MenubarSubTrigger>
                                <MenubarSubContent>
                                    <div className='flex flex-col gap-2 p-4'>
                                        <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                            Rescore selected candidates
                                            <span >
                                                ({selectedCandidates.length})
                                            </span>
                                            <ToolTip
                                                content='Select the candidates you want to move by checking the checkbox on their cards.'
                                                contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                            >
                                                <Info />
                                            </ToolTip>
                                        </h3>
                                        <div className='flex items-center gap-2 justify-end'>

                                            <Button className=""
                                                disabled={selectedCandidates.length < 1}
                                                size='tiny' onClick={openConfirmRescoreSelectedCandidateModal}
                                            >
                                                Rescore {selectedCandidates.length} candidates
                                            </Button>

                                        </div>
                                    </div>
                                    <MenubarSeparator className="border-b-2" />



                                    <div className='p-4'>
                                        <h3 className="flex items-center gap-2 hover:bg-white hover:text-header-text text-xs font-medium">
                                            Rescore candidates range
                                            <ToolTip
                                                content='Input the range of candidates you want to progress to the next stage. e.g. 1-5 to progress the first 5 candidates to the next stage.'
                                                contentClass='text-[0.78rem] leading-normal p-3 !max-w-[250px]'
                                            >
                                                <Info />
                                            </ToolTip>
                                        </h3>
                                        <section>
                                            <div className="grid grid-cols-2">
                                                <div className="inputdiv my-1">
                                                    <label className="!text-xs" htmlFor="start_move">From</label>
                                                    <input className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-sm !rounded-md !font-medium" id="end_move" name="start_move" type="number" value={startRescore}
                                                        onChange={(e) => setStartRescore(parseInt(e.target.value))}
                                                    />
                                                </div>
                                                <div className="inputdiv my-1">
                                                    <label className="!text-xs" htmlFor="start_move">To</label>
                                                    <input className="!p-[0.3rem] !w-[5rem] !bg-[#F5F7F9] !border-[0.75px] focus:!border-[0.75px] !text-sm !rounded-md !font-medium" id="end_move" name="start_move" type="number" value={endRescore}
                                                        onChange={(e) => setEndRescore(parseInt(e.target.value))}
                                                    />
                                                </div>
                                            </div>

                                            <div className='flex items-center justify-end gap-2 mt-4'>
                                                <Button className="mt-2"
                                                    disabled={startMove < 1 || endMove < 1}
                                                    size='tiny'
                                                    onClick={openConfirmRescoreRangeCandidatesModal}
                                                >Rescore candidates
                                                </Button>

                                            </div>
                                        </section>
                                    </div>
                                </MenubarSubContent>
                            </MenubarSub> */}



                        </MenubarContent>
                    </MenubarMenu>
                </Menubar>
            </section>
        </header>
    )
}

export default OrderMgtPipelineStageHeader