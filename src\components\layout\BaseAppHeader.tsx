'use client'

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components/ui';
import { Paybox360, ScanIcon, CameraIcon, LogoutIcon } from '@/components/icons';
import { TCartItem, TProduct } from '@/app/(main)/dashboard/misc/types';
import { Eye, LockIcon } from 'lucide-react';
import { format } from 'date-fns';
// import { UseGetSales } from '@/app/(main)/history/misc/api';
import { PaginationState } from '@tanstack/react-table';
import { useCompanyStore } from '@/stores';
import { useAuth } from '@/contexts';
import { cn } from '@/utils/classNames';

interface Product {
    id: string;
    name: string;
}

interface CartItem {
    id: string;
    quantity: number;
}

interface HeaderProps {
    showSearch?: boolean;
    activeTab?: 'sales' | 'transactions' | '';
    ordersCount?: number;
    userName: string;
    onAddToCart?: (product: TProduct) => void;
    onRemoveItem?: (productId: string) => void;
    cartItems?: TCartItem[];
    products?: TProduct[];
    isFetchingProducts?: boolean;
    className?: string;
}

const AppHeader: React.FC<HeaderProps> = ({
    showSearch = false,
    activeTab = '',
    ordersCount = 0,
    userName,
    onAddToCart,
    onRemoveItem,
    cartItems,
    products,
    isFetchingProducts, 
    className
}) => {
    const [searchTerm, setSearchTerm] = React.useState<string>('')
    const [{ pageIndex, pageSize }, setPagination] =
        React.useState<PaginationState>({
            pageIndex: 0,
            pageSize: 10,
        });

    const { company, branch, branchData } = useCompanyStore()
    // const fetchOptions = {
    //     company,
    //     branch,
    //     pageSize: pageSize,
    //     pageIndex: pageIndex,
    //     search: searchTerm
    // }

    // const { data } = UseGetSales(fetchOptions)

    return (
        <header className={cn('flex items-center justify-between gap-10 p-4 h-[8.5vh]', className && className)}>
            <section className='flex items-center gap-4'>
                <Paybox360 />
                <div className='bg-[#1E1E1E] p-1.5 rounded-lg max-h-14'>
                    <p
                        className={`flex flex-col p-1 px-4 rounded-lg bg-[#D9D9D91A]  text-white`}
                    >
                        <span className="text-[0.9rem] font-medium">
                            {branchData.company}
                        </span>
                        <span className='text-xs text-white/70'>
                            {branchData.name}
                        </span>
                    </p>
                </div>

                <div className='bg-[#1E1E1E] p-2 rounded-lg'>
                    <LinkButton
                        href="/history"
                        className={`px-8 py-4 rounded-lg ${activeTab === 'sales' ? 'bg-[#D9D9D91A]' : 'hover:bg-[#d9d9d911]'} h-9 text-white`}
                        variant="unstyled"
                    >
                        Sales
                    </LinkButton>
                    <LinkButton
                        href="/transactions"
                        className={`px-5 py-4 rounded-lg ${activeTab === 'transactions' ? 'bg-[#D9D9D91A]' : 'hover:bg-[#d9d9d911]'} h-9 text-white`}
                        variant="unstyled"
                    >
                        Transactions
                    </LinkButton>
                </div>
            </section>

            <div className='flex items-center gap-4'>
                <LinkButton className='flex items-center gap-2' href={showSearch ? "/history" : "/dashboard"} variant="secondary" size='thin'>
                    {showSearch ? 'Sales record' : 'Sell'}
                    <Eye size={20} className={showSearch ? '' : 'ml-4'} />
                </LinkButton>

                <LinkButton href='/transactions' variant="secondary" className='flex items-center w-12' size="icon">
                    <ScanIcon />
                </LinkButton>

                <Button variant="secondary" className='flex items-center w-12' size="icon">
                    <CameraIcon />
                </Button>

                <LinkButton
                    className='flex items-center gap-2 bg-[#1E1E1E] py-4'
                    href="/orders"
                    variant="secondary"
                    size='thin'
                >
                    {/* {Number(data?.data?.total_transactions) > 0 && (
                        <span className='flex items-center justify-center h-5 min-w-5 px-1 rounded-md bg-[#f2f5ff1f] text-xs'>
                            {
                                0
                            }
                        </span>
                    )} */}
                    Orders
                    <Eye size={20} />
                </LinkButton>

                <LinkButton href="/onboard/break" variant="secondary" className='flex items-center w-12' size="icon">
                    <LockIcon />
                </LinkButton>

                <Button className='flex items-center gap-2 py-0' variant="unstyled">
                    <div className='flex flex-col items-start gap-0'>
                        <p className='text-[0.925rem]'>{userName}</p>
                        <span className='text-muted-foreground text-left text-[0.75rem]'>{format(new Date(), 'yyyy-MM-dd')}</span>
                    </div>
                    <LinkButton href="/onboard/shift" className='flex items-center justify-center rounded-lg p-1 bg-[#3a1720ba]' variant="unstyled">
                        <LogoutIcon width={25} height={25} />
                    </LinkButton>
                </Button>
            </div>
        </header>
    );
};

export default AppHeader;