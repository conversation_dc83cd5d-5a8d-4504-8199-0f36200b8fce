'use client'
import React, { useMemo, useState } from 'react';
import { Draggable, DraggableProvided } from '@hello-pangea/dnd';
import { format } from 'date-fns';
// import toast from 'react-hot-toast';

import { Button, Checkbox, ConfirmActionModal, ConfirmDeleteModal, Popover, PopoverContent, PopoverTrigger } from '@/components/ui';
import { useBooleanStateControl } from '@/hooks';

import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from '../types';
import OrderDetailsDrawer from './OrderMgtPipelineStageOrderDrawer';
import { convertToTitleCase } from '@/utils/strings';
import { UseCancelOrder, UseMoveOrdertoNextStage } from '../api';
import { Ellipsis, EllipsisVertical } from 'lucide-react';
import { cn } from '@/utils/classNames';
import { BadgeCheckIcon, BadgeErrorIcon } from '../icons';
import toast from 'react-hot-toast';
import { Elipsis } from '@/components/icons';

interface CardProps {
    className?: string
    index: number;
    order: TOrderMgtPipelineStageOrder;
    allOrdersId: string[];
    allOrdersData: TOrderMgtPipelineStageOrder[];
    branchId: string
    selectedOrders: string[]
    setSelectedOrders: React.Dispatch<React.SetStateAction<string[]>>
    refetchPipelineData: () => void
    nextStageId?: string
    pipelineId: string
    currentStage: OrderMgtPipelineStageDetails
}


const OrderMgtPipelineStageOrderCard: React.FC<CardProps> = ({ index, className, order, allOrdersData, allOrdersId, nextStageId, pipelineId, refetchPipelineData, currentStage, selectedOrders, setSelectedOrders }) => {
    // const { order_date, id: order_id, buyer, status, payment_status, amount_paid, order_time } = order
    const { order_date, id: order_id, buyer, status, payment_status, order_time } = order
    const ordersIDs = useMemo(() => allOrdersId, [allOrdersId])
    const ordersData = useMemo(() => allOrdersData, [allOrdersData])
    const [hours, minutes, secondsWithFraction] = order_time.split(':');
    const seconds = parseInt(secondsWithFraction.split('.')[0]);

    const date = new Date(order_date);
    date.setHours(parseInt(hours));
    date.setMinutes(parseInt(minutes));
    date.setSeconds(seconds);
    const formattedTime = format(date, 'h:mm a');

    const localIndex = useMemo(() => {
        return index
    }, [index])


    const {
        state: isDrawerOpen,
        setTrue: openDrawer,
        setFalse: closeDrawer,
    } = useBooleanStateControl(false)

    const {
        state: isConfirmProgressModalOpen,
        setTrue: openConfirmProgressModal,
        setFalse: closeConfirmProgressModal,
    } = useBooleanStateControl(false)

    const {
        state: isConfirmCancelOrderModalOpen,
        setTrue: openConfirmDeleteOrderModal,
        setFalse: closeConfirmCancelOrderModal,
    } = useBooleanStateControl(false)




    const { mutate: moveOrderToNextStage, isPending: isMovingOrder } = UseMoveOrdertoNextStage()
    const { mutate: cancelTheOrder, isPending: isCancellingOrder } = UseCancelOrder()
    const progressOrder = () => {
        if (nextStageId) {
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: [order_id],
                pipeline_id: pipelineId
            }, {
                onSuccess() {
                    toast.success('Order moved successfully');
                    refetchPipelineData()
                    closeConfirmProgressModal()
                },
            })
        }
    }

    const cancelOrder = () => {
        cancelTheOrder({
            //@ts-ignore
            order_ids: [order_id] || [""],
        }, {
            onSuccess: () => {
                toast.success('Order cancelled successfully');
                refetchPipelineData()
                closeConfirmCancelOrderModal()
            }
        })
    }




    return (
        <Draggable draggableId={String(order_id)} index={localIndex}>
            {(provided: DraggableProvided) => (
                <article
                    className={cn('grid grid-cols-[1fr,max-content] gap-1.5 bg-[#1C1C1C] p-4 rounded-xl !text-[0.85rem] mb-4 w-full max-w-[400px]',
                        selectedOrders.includes(order_id) && "border-primary", className
                    )}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                >
                    <section className='flex flex-col gap-2.5'>
                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-[0.7rem]'>
                            <p className='flex flex-col text-[#7D8590]'>
                                <span className='flex items-center gap-1.5'>
                                    <span className='text-white font-medium'>
                                        {order.order_products[0]?.product_name}
                                    </span>
                                    <span className='font-semibold text-white px-1 py-0.5 rounded-md bg-main-solid-light text-[0.65rem]'>
                                        {order.order_products?.length > 1 && ` +${order.order_products?.length - 1}`}
                                    </span>
                                </span>
                                Item
                            </p>
                            <p className='flex flex-col text-[#7D8590] text-left truncate'><span className='text-white font-medium truncate'>{formattedTime}</span> Time</p>
                        </div>


                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-[0.7rem]'>
                            <p className='flex flex-col text-[#7D8590]'><span className='text-white font-medium'>{convertToTitleCase(`${buyer.first_name} ${buyer.last_name}`)}</span> Name</p>
                            <p className='flex flex-col text-[#7D8590] text-left truncate'><span className='text-white font-medium truncate'>{buyer.phone_number}</span> Phone number</p>
                        </div>


                        <div className='grid grid-cols-[1fr,0.8fr] gap-8 text-[0.7rem]'>
                            <div className='flex items-center gap-4'>
                                <p
                                    className={
                                        cn('text-[0.7rem] font-medium rounded-lg px-4 py-1',
                                            (status === "OPEN" || status === "ACCEPTED") && '!bg-[#FFF8E7] !text-[#C78C00]',
                                            status == "confirmed" && 'bg-[#EBF6F0] text-[#009444]',
                                            status == "CANCELLED" && 'bg-[#FFEDED] text-[#FF5C5C]',
                                        )
                                    }
                                >
                                    {convertToTitleCase(status)}
                                </p>
                                <p
                                    className={
                                        cn('text-[0.7rem] font-medium rounded-lg px-4 py-1',
                                            payment_status == "paid" ? 'bg-[#EBF6F0] text-[#009444]' : 'bg-[#FFEDED] text-[#FF5C5C]',
                                        )
                                    }
                                >
                                    {payment_status}
                                </p>
                            </div>
                            <OrderDetailsDrawer
                                allOrders={ordersData}
                                allOrdersId={ordersIDs}
                                closeDrawer={closeDrawer}
                                isDrawerOpen={isDrawerOpen}
                                isMovingOrder={isMovingOrder}
                                openDrawer={openDrawer}
                                order_id={order_id}
                                progressOrder={progressOrder}
                                refetchPipelineData={refetchPipelineData}
                                stage={currentStage}
                            />
                        </div>
                    </section>

                    <section className='flex flex-col items-center justify-between ml-auto h-full'>
                        <Popover>
                            <PopoverTrigger className='px-2'>
                                <Elipsis />
                            </PopoverTrigger>

                            <PopoverContent align='end' className='flex flex-col max-w-max p-2 items-stretch'>
                                <button className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text' onClick={openConfirmProgressModal}>
                                    Progress Order
                                </button>
                                <button className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text' onClick={openConfirmDeleteOrderModal}>
                                    Cancel Order
                                </button>
                            </PopoverContent>
                        </Popover>
                        <Checkbox
                            checked={selectedOrders.includes(order_id)}
                            className='p-0.5'
                            // circle
                            onCheckedChange={() => {
                                const updatedSelectedOrders = selectedOrders.includes(order_id)
                                    ? selectedOrders.filter(id => id !== order_id)
                                    : [...selectedOrders, order_id];
                                setSelectedOrders(updatedSelectedOrders);
                            }}
                        />
                    </section>




                    {/* /////////////////////////////////////////////////////// */}
                    {/* /////////////////////////////////////////////////////// */}
                    <ConfirmActionModal
                        closeModal={closeConfirmProgressModal}
                        confirmFunction={progressOrder}
                        isConfirmingAction={isMovingOrder}
                        isModalOpen={isConfirmProgressModalOpen}
                        title="Move order to next stage"
                    >
                        <p className='text-[#b6b6c0] text-sm font-normal'>
                            You are about to move order with Id <span className='text-header-text font-bold mr-1'>#{order.id}</span> to the next stage.
                        </p>
                    </ConfirmActionModal>

                    <ConfirmActionModal
                        closeModal={closeConfirmCancelOrderModal}
                        confirmFunction={cancelOrder}
                        isConfirmingAction={isCancellingOrder}
                        isModalOpen={isConfirmCancelOrderModalOpen}
                        title="Cancel Order"
                    >
                        <div className='text-[#b6b6c0] text-sm font-normal '>
                            <p className="text-center">You are about to cancel order with ID  <span className='text-header-text  font-bold mx-1'>#{order_id}</span> from the pipeline,
                            </p>
                        </div>
                    </ConfirmActionModal>

                    {/* <LoadingOverlay isOpen={isMovingOrder || isRemovingOrder} /> */}
                </article>
            )}
        </Draggable>
    );
};

export default OrderMgtPipelineStageOrderCard;