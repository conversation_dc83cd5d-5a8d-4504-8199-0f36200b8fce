
import React, { Dispatch, SetStateAction } from 'react';
import { Droppable } from '@hello-pangea/dnd';

import { ConfirmActionModal, ConfirmDeleteModal } from '@/components/ui';

import OrderMgtPipelineStageHeader from './OrderMgtPipelineStageHeader';
import { OrderMgtPipelineStageDetails } from '../../types';
import OrderMgtPipelineStageOrderCard from './OrderMgtPipelineStageOrderCard';
import { useBooleanStateControl } from '@/hooks';
import { UseMoveOrdertoNextStage } from '../../api';

interface OrderMgtPipelineStageProps {
    branch_id: string
    pipeline_id: string
    data: OrderMgtPipelineStageDetails
    filterUrl: string
    setFilterUrl: Dispatch<SetStateAction<string>>
    allStages: OrderMgtPipelineStageDetails[]
    refetchJobData: () => void
}


const OrderMgtPipelineStage: React.FC<OrderMgtPipelineStageProps> = ({ data, filterUrl, branch_id, pipeline_id, allStages, refetchJobData }) => {

    const [ordersToDisplay, setOrdersToDisplay] = React.useState(data.data)
    const [selectedOrders, setSelectedOrders] = React.useState<string[]>([])
    const nextStageId = allStages.findIndex((stage) => stage.stage_id === data.stage_id) + 1 < allStages.length ? allStages[allStages.findIndex((stage) => stage.stage_id === data.stage_id) + 1].stage_id : undefined

    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////                          BOOLEAN STATES                     //////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    const {
        state: isConfirmProgressModalOpen,
        setTrue: openConfirmProgressModal,
        setFalse: closeConfirmProgressModal,
    } = useBooleanStateControl(false)



    React.useEffect(() => {
        setOrdersToDisplay(data.data)
    }, [data])



    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    //////////////////                          BOOLEAN STATES                     //////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////

    const { mutate: moveOrderToNextStage, isPending: isMovingOrder } = UseMoveOrdertoNextStage()
    const progressOrder = () => {
        if (nextStageId) {
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: selectedOrders,
                pipeline_id

            })
        }
    }


    return (
        <Droppable droppableId={data.stage_id}>
            {(provided) => (
                <article className='flex flex-col !shrink-0  gap-2 mt-0 pt-1 px-2 h-full overflow-y-scroll max-md:min-w-[90vw]  w-max min-w-[200px] lg:w-[400px] max-w-[425px] '>
                    {/* <a className='hidden' href="" ref={xlsxRef}></a> */}
                    <OrderMgtPipelineStageHeader
                        filterUrl={filterUrl}
                        openConfirmProgressModal={openConfirmProgressModal}
                        openPreviewModal={() => {
                            //
                        }}
                        openSearchBox={() => {
                            //
                        }}
                        searchText={""}
                        stage={data}
                    />

                    {/* {
                        isSearchBoxOpen &&
                        <div className='relative grid grid-cols-[1fr,0.2fr,max-content] max-w-full items-center justify-between gap-2 border-[1.6px] border-[#D6D6D6] p-4 py-1 rounded-lg focus-within:border-primary'>
                            <input
                                className=' outline-none  text-sm py-1'
                                placeholder='start typing'
                                ref={searchTextBoxRef}
                                type="text"
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                            />
                            <Select
                                className='!p-1 !text-xs max-md:max-w-[6rem] md:max-lg:max-w-[5rem] lg:max-w-[7rem]'
                                containerClass='!my-0'
                                errors={{ null: { message: "" } }}
                                itemClass='!text-xs py-1'
                                name='search_by'
                                options={searchFilters}
                                placeholder='Search by'
                                value={searchBy}
                                onChange={(e) => setSearchBy(e)}
                            />
                            <Button className='w-6 h-6 bg-[#F9FAFB] hover:bg-red-300 !rounded-full' size='icon' variant='unstyled' onClick={() => { closeSearchBox(); setSearchText("") }}>
                                <XIcon />
                            </Button>
                        </div>
                    } */}




                    <section {...provided.droppableProps} className='flex flex-col grow overflow-y-scroll p-4 bg-black rounded-b-xl' ref={provided.innerRef}>
                        {
                            ordersToDisplay?.map((order, index) => (
                                <OrderMgtPipelineStageOrderCard
                                    allOrdersId={ordersToDisplay.map((order) => order.id)}
                                    allOrders={ordersToDisplay}
                                    branch_id={branch_id}
                                    index={index}
                                    key={index}
                                    nextStageId={nextStageId}
                                    order={order}
                                    pipelineId={pipeline_id}
                                    refetchJobData={refetchJobData}
                                    selectedOrders={selectedOrders}
                                    setSelectedOrders={setSelectedOrders}
                                    stage={data}
                                />
                            ))
                        }
                        {provided.placeholder}
                    </section>






                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* //////////////                           MODALS                           //////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}
                    {/* ////////////////////////////////////////////////////////////////////////////////////// */}

                    <ConfirmDeleteModal
                        closeModal={() => {
                            //
                        }}
                        deleteFunction={() => {
                            //
                        }}
                        isModalOpen={false}
                        title="Delete Candidate(s)"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            {/* You are about to delete candidates  <span className='text-header-text font-bold ml-1'>{startRemove}</span> - <span className='text-header-text font-bold mr-1'>{endRemove}</span> */}
                            from the pipeline. Please be aware that all candidates in the range will be deleted from the pipeline but can still be seen in the candidates tab.
                        </p>
                    </ConfirmDeleteModal>

                    <ConfirmActionModal
                        closeModal={closeConfirmProgressModal}
                        confirmFunction={progressOrder}
                        isConfirmingAction={isMovingOrder}
                        isModalOpen={isConfirmProgressModalOpen}
                        title="Progress candidates to next stage"
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            You are about to move  <span className='text-header-text font-bold mr-1'>{selectedOrders} orders</span> to the next stage.

                            Please be aware that any candidates progressed to a stage with email notification enabled will <span className='text-header-text font-medium mr-1'>receive an email</span> from us even if you move them back later.
                        </p>
                    </ConfirmActionModal>

                    {/* 
                    <InfoModal
                        isModalOpen={isInformEmailNotificationModalOpen}
                        closeModal={closeInformEmailNotificationModal}
                        title="Email notification notice"
                        confirmFunction={closeInformEmailNotificationModal}
                    >
                        <p className='text-[#8C8CA1] text-sm font-normal'>
                            We just sent an email to all the candidates you just progressed to this stage <span className='text-header-text font-medium mr-1'>{stage_name}</span>.{" "}
                            If you want to stop this behaviour, turn off <span className='text-header-text font-medium mr-1'>email notification</span> in the stage configuration.
                        </p>
                    </InfoModal> */}


                </article>
            )}
        </Droppable>
    );
};

export default OrderMgtPipelineStage;