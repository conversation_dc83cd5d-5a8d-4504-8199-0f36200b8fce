import { Input } from "@/components/ui";
import React, { useState, useRef, useEffect } from "react";
import { TCartItem, TProduct } from "../types";
import { ProductItemRow } from "./ProductItemRow";
import { cn } from '@/utils/classNames'

interface SearchBoxProps {
    products: TProduct[];
    isFetchingProducts: boolean;
    onAddToCart: (product: TProduct) => void;
    cartItems: TCartItem[];
    onRemoveItem: (id: string) => void;
    setSearchTerm: (v: string) => void;
}

export const SearchBox = React.forwardRef<HTMLInputElement, SearchBoxProps>((props, ref) => {
    const { products, isFetchingProducts, onAddToCart, cartItems, onRemoveItem, setSearchTerm } = props;
    const [focusedIndex, setFocusedIndex] = useState(-1);
    const [inputValue, setInputValue] = useState("");
    const [productsToDisplay, setProductsToDisplay] = useState<TProduct[]>([]);

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'ArrowDown') {
                // event.preventDefault();
                setFocusedIndex(prev => Math.min(prev + 1, productsToDisplay.length - 1));
            } else if (event.key === 'ArrowUp') {
                event.preventDefault();
                setFocusedIndex(prev => Math.max(prev - 1, -1));
            } else if (event.key === 'Enter' && focusedIndex >= 0) {
                onAddToCart(productsToDisplay[focusedIndex]);
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [focusedIndex, productsToDisplay, onAddToCart]);

    const handleSearch = (query: string) => {
        setSearchTerm(query)
        setInputValue(query);
        const filteredResults = products.filter(product =>
            product.item.toLowerCase().includes(query.toLowerCase())
        );
        setProductsToDisplay(filteredResults);
        setFocusedIndex(-1);
    };


    return (
        <div className="relative grow ml-auto max-w-[600px] group/container focus-within:block">
            <Input
                className="h-[2.5rem]"
                ref={ref}
                type="text"
                placeholder="Search"
                value={inputValue}
                onChange={(e) => handleSearch(e.target.value)}
            />
            {
                productsToDisplay.length > 0 && (
                    <ul
                        className={cn(
                            "absolute grid grid-cols-[1fr,max-content,0.75fr] w-full left-0 top-12 bg-[#1C1C1C] p-1 rounded-lg max-h-96 overflow-y-auto",
                            "divide-y-[1.2px] divide-[#262729] hidden group-focus-within/container:grid"
                        )}
                    >
                        {productsToDisplay.map((product, index) => (
                            <ProductItemRow
                                key={product.item_id}
                                product={product}
                                onAddToCart={onAddToCart}
                                isFocused={index === focusedIndex}
                                cartItems={cartItems}
                                onRemoveFromCart={onRemoveItem}
                            />
                        ))}
                    </ul>
                )
            }
        </div>
    );
});

SearchBox.displayName = 'SearchBox';