"use client"

import { Eye, LockIcon } from "lucide-react"
import React, { type KeyboardEvent, useEffect, useRef, useState } from "react"

import { LogoutIcon, Paybox360, ScanIcon } from "@/components/icons"
import { <PERSON><PERSON>, LinkButton } from "@/components/ui"
import { useCartStore, useCompanyStore } from "@/stores"

import { convertToTitleCase } from "@/utils/strings"
import type { PaginationState } from "@tanstack/react-table"
import Link from "next/link"
import { UseGetCategories, UseGetSubCategories, UseGetProducts } from "./misc/api"
import { Cart, CategoriesList, Checkout, ProductContainer, SearchBox, Summary } from "./misc/components"
import type { TProduct } from "./misc/types"
import { type Itag, PriceTag } from "./misc/components/Pricetag"
import { useGetPriceTags } from "./misc/api/getProducts"
import { useDebounce } from "@/hooks"
import useInfiniteScroll from "react-infinite-scroll-hook"
import { Refresh } from "iconsax-react"
import { useRouter } from "next/navigation"
import SubCategoriesList from "./misc/components/ProductSubCategoriesList"
import { useBarcodeScanner } from "@/hooks/useBarcodeScanner"

const SalesDashboard: React.FC = () => {
  const [isHardRefresh, setIsHardRefresh] = useState<boolean>(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [sales_tag, setSales_tag] = useState<string | null>(null)
  const [selectedSubCategory, setSelectedsubCategory] = useState<string | null>(null)
  const [discountValue, setDiscountValue] = React.useState(0)
  const [overallPrice, setOverallPrice] = React.useState(0)
  const [selectedPriceTag, setSelectedPriceTag] = React.useState<Itag>({
    name: "Selling Price",
    id: "",
  })
  const [searchTerm, setSearchTerm] = React.useState<string>("")
  const searchQuery = useDebounce(searchTerm, 500)

  const { branch, company, first_name, last_name, branchData } = useCompanyStore()
  const fetchOptions = {
    company,
    branch,
    category: selectedCategory,
    subcategory: selectedSubCategory,
    search: searchQuery,
    pageIndex: 1,
    pageSize: 100,
    price_tag: selectedPriceTag.name === "Selling Price" ? "" : selectedPriceTag.id,
  }
  const { data: priceTags } = useGetPriceTags(
    { companyId: company },
    //   initialCategoriesResponse
  )
  const {
    data,
    isLoading: isFetchingProducts,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = UseGetProducts(fetchOptions)

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: "0px 0px 800px 0px",
  })

  const searchRef = useRef<HTMLInputElement>(null)
  const cartRef = useRef<HTMLDivElement>(null)
  const summaryRef = useRef<HTMLDivElement>(null)
  const checkoutRef = useRef<HTMLDivElement>(null)
  const productsContainerRef = useRef<HTMLUListElement>(null)
  const categoriesListRef = useRef<HTMLDivElement>(null)
  const subcategoriesListRef = useRef<HTMLDivElement>(null)

  const router = useRouter()
  const { active_cart, update_item_quantity, delete_item, add_item, remove_item, clear_cart } = useCartStore()
  const [paymentMethod, setPaymentMethod] = useState<string>("CASH")

  const available_stock = data && data?.pages.flatMap((page) => page?.data?.stock)

  useBarcodeScanner({
    products: available_stock,
    onProductFound: add_item,
  })

  useEffect(() => {
    let lastKey = ""
    let lastKeyTime = 0
    const pressedKeys = new Set<string>()

    const handleKeyDown = (event: KeyboardEvent) => {
      const currentTime = new Date().getTime()
      pressedKeys.add(event.key.toLowerCase())
      if (event.ctrlKey) {
        switch (event.key) {
          case "s":
            event.preventDefault()
            searchRef.current?.focus()
            break
          case "c":
            event.preventDefault()
            cartRef.current?.focus()
            break
          case "d":
            event.preventDefault()
            productsContainerRef.current?.focus()
            break
          case "l":
            event.preventDefault()
            categoriesListRef.current?.focus()
            break
          case "m":
            event.preventDefault()
            checkoutRef.current?.focus()
            break
        }
      }

      if (event.key === "Escape") {
        event.preventDefault()
        ;(document.activeElement as HTMLElement)?.blur()
      }

      if (event.key === "c" && lastKey === "c" && currentTime - lastKeyTime < 1000) {
        event.preventDefault()
        cartRef.current?.focus()
      }

      if (event.key === "p" && lastKey === "p" && currentTime - lastKeyTime < 1000) {
        productsContainerRef.current?.focus()
      }

      if (event.key === "s" && lastKey === "s" && currentTime - lastKeyTime < 1000) {
        event.preventDefault()
        searchRef.current?.focus()
      }

      if (event.key === "a" && lastKey === "a" && currentTime - lastKeyTime < 1000) {
        // event.preventDefault();
        checkoutRef.current?.focus()
      }
      if (event.key === "c" && lastKey === "h" && currentTime - lastKeyTime < 1000) {
        // event.preventDefault();
        checkoutRef.current?.focus()
      }

      if (pressedKeys.has("c") && pressedKeys.has("h")) {
        checkoutRef.current?.focus()
      }

      lastKey = event.key
      lastKeyTime = currentTime
    }

    const handleKeyUp = (event: KeyboardEvent) => {
      pressedKeys.delete(event.key.toLowerCase())
    }

    window.addEventListener("keydown", handleKeyDown as any)
    window.addEventListener("keyup", handleKeyUp as any)

    return () => {
      window.removeEventListener("keydown", handleKeyDown as any)
      window.removeEventListener("keyup", handleKeyUp as any)
    }
  }, [])

  const [productsToDisplay, setProductsToDisplay] = useState<TProduct[] | undefined>(
    available_stock || ([] as TProduct[]),
  )

  useEffect(() => {
    if (!isFetchingProducts && data) {
      setProductsToDisplay(available_stock)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFetchingProducts, data])

  useEffect(() => {
    if (branch) {
      active_cart.map((v) => {
        if (v.branch !== branch) {
          clear_cart()
        }
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [branch])
  useEffect(() => {
    if (isHardRefresh) {
      clear_cart()
      // router.push("/refresh")
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isHardRefresh])

  let todaysDate
  const date = new Date().getDate().toString().padStart(2, "0")
  const month = (new Date().getMonth() + 1).toString().padStart(2, "0")
  const year = new Date().getFullYear()
  todaysDate = `${date}/${month}/${year}`

  const { data: categories, isLoading: isFetchingCategories } = UseGetCategories(company)
  const { data: subcategories, isLoading: isFetchingSubCategories } = UseGetSubCategories(company)
  const handleCategorySelect = (category: { name: string; id: string }) => {
    setSelectedCategory(category.id)
    if (category && data && available_stock) {
      setProductsToDisplay(available_stock?.filter((item) => item.category_id === category.id))
    } else {
      setProductsToDisplay(available_stock)
    }
  }
  const handleSubCategorySelect = (subcategory: {
    name: string
    id: string
  }) => {
    setSelectedsubCategory(subcategory.id)
    if (subcategory && data && available_stock) {
      setProductsToDisplay(available_stock?.filter((item) => item.subcategory_id === subcategory.id))
    } else {
      setProductsToDisplay(available_stock)
    }
  }

  const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  // const fetchSalesOptions = {
  //   company,
  //   branch,
  //   pageSize: pageSize,
  //   pageIndex: pageIndex,
  //   search: "",
  // };

  // const { data: salesData } = UseGetSales(fetchSalesOptions);

  return (
    <div className="grid grid-rows-[max-content,1fr] flex-col w-screen h-screen overflow-hidden">
      <header className="flex items-center justify-between gap-10 p-4 h-[8.5vh]">
        <Paybox360 />
        <div className="bg-[#1E1E1E] p-1.5 rounded-lg max-h-14">
          <p className={`flex flex-col p-1 px-4 rounded-lg bg-[#D9D9D91A]  text-white`}>
            <span className="text-[0.9rem] font-medium">{branchData.company}</span>
            <span className="text-xs text-white/70">{branchData.name}</span>
          </p>
        </div>

        <SearchBox
          setSearchTerm={setSearchTerm}
          ref={searchRef}
          products={available_stock!}
          isFetchingProducts={isFetchingProducts}
          onAddToCart={add_item}
          onRemoveItem={remove_item}
          cartItems={active_cart}
        />

        <div className="flex items-center gap-4">
          <PriceTag
            tags={(priceTags?.price_tags as any)?.concat([{ name: "Selling Price" }]) ?? []}
            value={selectedPriceTag}
            onValueChange={(e) => {
              setSelectedPriceTag(e)
            }}
          />
          <LinkButton className="flex items-center gap-2" href="/history" variant="secondary" size="thin">
            Sales record
            <Eye size={20} />
          </LinkButton>
         
          <LinkButton href="/transactions" variant="secondary" className="flex items-center w-12" size="icon">
            <ScanIcon />
          </LinkButton>
          {/* 
          <Button variant="secondary" className='flex items-center w-12' size="icon">
            <CameraIcon />
          </Button> */}

          <LinkButton className="flex items-center gap-2" href="/orders" variant="secondary" size="thin">
            {/* {Number(salesData?.data?.total_transactions) > 0 && (
              <span className='flex items-center justify-center h-5 min-w-5 px-1 rounded-md bg-[#f2f5ff1f] text-xs'>
                {
                  0
                }
              </span>
            )} */}
            Orders
            <Eye size={20} />
          </LinkButton>

          <LinkButton href="/onboard/break" variant="secondary" className="flex items-center w-12" size="icon">
            <LockIcon />
          </LinkButton>

          <Button className="flex items-center gap-2 py-0" variant="unstyled">
            <div className="flex flex-col items-start gap-0">
              <p className="text-[0.85rem]">{`${convertToTitleCase(first_name)} ${convertToTitleCase(last_name)}`}</p>
              <span className="text-muted-foreground text-left text-[0.7rem]">{todaysDate}</span>
            </div>
            <Link className="flex items-center justify-center rounded-lg p-1 bg-[#3a1720ba]" href="/onboard/shift">
              <LogoutIcon width={20} height={20} />
            </Link>
          </Button>
        </div>
      </header>

      <main className="w-full grow grid grid-cols-[1fr,0.45fr] 2xl:grid-cols-[1fr,500px] h-[91.5vh]">
        <div className="overflow-y-scroll [scrollbar-width:none_!important]">
          <CategoriesList
            categories={categories?.data.categories.map((category) => category) || []}
            isLoading={isFetchingCategories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
            ref={categoriesListRef}
          />
          <SubCategoriesList
            categories={subcategories?.data.subcategories.map((subcategory) => subcategory) || []}
            isLoading={isFetchingSubCategories}
            selectedCategory={selectedSubCategory}
            onCategorySelect={handleSubCategorySelect}
            ref={subcategoriesListRef}
          />

          <ProductContainer
            products={productsToDisplay || ([] as TProduct[])}
            isFetchingProducts={isFetchingProducts}
            onAddToCart={add_item}
            onRemoveFromCart={remove_item}
            cartItems={active_cart}
            ref={productsContainerRef}
            branch={branch}
            company={company}
            loadMoreDataObserver={<div ref={sentryRef} />}
          />
        </div>

        <aside className="relative flex flex-col gap-2.5 pl-1 pr-5 pt-0 pb-4 overflow-y-scroll  h-[91.5vh]">
          <Button
            onClick={() => {
              setIsHardRefresh(true)
            }}
            className="hover:bg-[#1C1C1F] flex items-center justify-between bg-[#1C1C1C] text-white"
          >
            <span>Refresh</span>
            <Refresh className={isHardRefresh ? "animate animate-spin" : ""} />
          </Button>
          <div className="flex flex-col gap-2.5 grow overflow-y-scroll">
            <Cart
              items={active_cart}
              onUpdateQuantity={update_item_quantity}
              onRemoveItem={delete_item}
              ref={cartRef}
              available_stock={available_stock!}
            />

            <Summary
              cart={active_cart}
              ref={summaryRef}
              paymentMethod={paymentMethod}
              setOverallPrice={setOverallPrice}
              discountValue={discountValue}
              setDiscountValue={setDiscountValue}
              setSales_tag={setSales_tag}
              sales_tag={sales_tag ?? ""}
            />
          </div>

          <Checkout
            sales_tag={sales_tag ?? ""}
            discountValue={discountValue}
            setDiscountValue={setDiscountValue}
            ref={checkoutRef}
            paymentMethod={paymentMethod}
            setPaymentMethod={setPaymentMethod}
            overallPrice={overallPrice}
          />
        </aside>
      </main>
    </div>
  )
}

export default SalesDashboard
