import { useAuth } from "@/contexts";
import { managementAxios } from "@/lib/axios";
import { useMutation } from "@tanstack/react-query";

const startBreak = async () => {
    const breakTime = await managementAxios.post(`/core/start_sales_shift_break/`);
    return breakTime.data
}

const UseStartBreak = () => {
  const { isAuthenticated, isLoading, shiftStatus, refetchShiftStatus } = useAuth();

    return useMutation({
        mutationFn: startBreak,
        onSuccess(data, variables, context) {
            refetchShiftStatus()
        },
    })
}

export default UseStartBreak;