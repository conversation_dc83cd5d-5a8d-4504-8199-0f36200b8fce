'use client'
import { AxiosError } from 'axios';

import { managementAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';
import { TCustomer, TNewCustomer } from '../types';

  

type ApiResponse = {
    status: string;
    status_code: number;
    data: TCustomer 
    errors: any;
};

const addCustomer = async (data: TNewCustomer): Promise<ApiResponse> => {
    const response = await managementAxios.post<ApiResponse>(`/api/v1/sales/customers/`, data);
    return response.data;
}

export const useAddCustomer = () => {
    return useMutation<ApiResponse, AxiosError<ApiResponse>, TNewCustomer>({
        mutationFn: addCustomer,
    });
};
