'use client'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { z, ZodError } from "zod";

import { Input, Skeleton, Button } from '@/components/ui'
import { zodResolver } from '@hookform/resolvers/zod';
import { PayboxLogo, SmallSpinner } from '@/components/icons';

import { useResetPasscode } from '../misc/api/postResetPasscode';
import { cn } from '@/utils/classNames';


const LoginSchema = z.object({
  username: z.string(),
  default_password: z.string().min(6).max(6),
  password: z.string().min(6, { message: "Password must be six digits" }).max(6)
})
type TDefaultData = {
  username: string;
  password: string;
}
export type TResetPasscode = z.infer<typeof LoginSchema>



const UnauthenticatedUserHomepage = () => {
  const [defaultUserData, setDefaultUserData] = useState<TDefaultData | null>(null)
  const router = useRouter()
  const {
    register, control, handleSubmit, setValue, watch, setError, clearErrors, formState: { isValid, errors }, reset
  } = useForm<TResetPasscode>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      username: defaultUserData?.username || '',
      default_password: defaultUserData?.password || '',
    }
  });


  React.useEffect(() => {
    if (typeof window !== 'undefined' && !!localStorage.getItem('newUser')) {
      const newUser = JSON.parse(localStorage.getItem('newUser')!)
      setValue('username', newUser.username)
      setValue('default_password', newUser.password)

    }
    else {
      setDefaultUserData(null)
    }
  }, [])

  const { mutate: resetPasscode, isPending } = useResetPasscode()

  const SubmitForm = async (data: TResetPasscode) => {
    console.log(data)
    resetPasscode(data, {
      onSuccess: (res) => {
        router.replace('/login')
      },
      onError: (error) => {
        if (error?.response?.data.message == "Invalid default passcode") {
          setError('default_password', {
            type: 'manual',
            message: 'Invalid default passcode'
          })
        }
      }
    })
  }


  return (
    <div className='size-full flex items-center justify-center'>
      <article className='flex flex-col items-center justify-center gap-4 w-full max-w-[520px] min-h-[320px] mx-auto p-4 py-16 rounded-xl bg-[#1c1c1c96]'>
        <div className={cn('flex flex-col items-center gap-2')}>
          <PayboxLogo width={70} height={70} />
          <h3 className='font-clash text-3xl font-semibold'>Create passcode</h3>
        </div>

        <form action="" className='flex flex-col gap-4 w-full md:w-[80%] my-12' onSubmit={handleSubmit(SubmitForm)}>
          <Input
            type='text'
            placeholder='Username'
            className='bg-transparent' {...register('username')}
            readOnly
          />
          <Input
            type='password'
            placeholder='Enter Password'
            className='bg-transparent'  {...register('default_password')}
            hasError={!!errors.default_password}
            errorMessage={errors.default_password?.message}
          />
          <Input
            type='password'
            placeholder='New Password'
            className='bg-transparent'  {...register('password')}
            hasError={!!errors.password}
            errorMessage={errors.password?.message}
          />

          <Button className='mt-12 w-full'>
            Continue
            {
              isPending && <SmallSpinner color="black" className='ml-2' />
            }
          </Button>
        </form>
      </article>
    </div>
  )
}

export default UnauthenticatedUserHomepage