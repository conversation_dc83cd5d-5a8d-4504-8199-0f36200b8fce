import React from 'react';
import { format } from 'date-fns';
import { Box, ClipboardCheck, PenSquareIcon, Send } from 'lucide-react';
import { TorderMgtOrderTrailEvent } from '../types/orderManagement';

// type TorderMgtOrderTrailEvent = {
//   event: string;
//   timestamp: string;
//   stage_name: string;
// };

interface TimelineProps {
  data: TorderMgtOrderTrailEvent[];
}

const OrderMgtPipelineStageOrderTrail: React.FC<TimelineProps> = ({ data }) => {
  const getEventIcon = (event: TorderMgtOrderTrailEvent) => {
    switch (event.event) {
      case 'new order':
        return <ClipboardCheck className="text-primary" size={20} />;
      case 'order accepted':
        return <Box className="text-primary" size={20} />;
      case 'order cancelled':
        return <Box className="text-primary" size={20} />;
      case 'progression':
        return <Send className="text-primary" size={20} />;
      case 'percentage_match_update':
        return <PenSquareIcon height={20} width={20} />;
      default:
        return <div className="w-[20px] h-[20px] bg-primary rounded-full"></div>;
    }
  };

  const getEventName = (event: TorderMgtOrderTrailEvent) => {
    switch (event.event) {
      case 'new order':
        return "New Order";
      case 'progression':
        return "Moved to a new stage";
      case 'order accepted':
        return "Order Accepted";
      case 'order cancelled':
        return "Order Cancelled";
      default:
        return "Event";
    }
  };

  return (
    <div className="relative">
      <div className="border-l border-primary mx-4">
        {
          data?.map((event, index) => (
            <div className="mb-8 relative" key={index}>
              <div className="absolute p-1.5 left-[-19px] mt-1 bg-secondary rounded-full">{getEventIcon(event)}</div>
              <div className="ml-8">
                <p className="text-header-text font-medium">{getEventName(event)}</p>
                <p className="text-sm text-helper-text">
                  <span className='text-header-text'>Date: </span> {format(new Date(event.timestamp), 'MMM d, yyyy h:mm aaaa')}
                </p>
                {
                  event.stage_name && (
                    <p className="text-sm text-helper-text">
                      <span className='text-header-text'>New Stage: </span> {event.stage_name}
                    </p>
                  )
                }
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default OrderMgtPipelineStageOrderTrail;
