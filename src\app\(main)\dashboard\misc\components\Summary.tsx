import { SmallSpinner } from "@/components/icons";
import { Button, Input } from "@/components/ui";
import { useCartStore, useCompanyStore } from "@/stores";
import { convertNumberToNaira } from "@/utils/currency";
import React, { useEffect } from "react";
import toast from "react-hot-toast";
import { UseSaveSale } from "../api";
import { TCartItem } from "../types";
import { useSearchParams } from "next/navigation";
import { UseUpdateSavedSale } from "../api/postSaveSale";
import { useChangeQueryParams } from "@/hooks";

interface SummaryProps {
  cart: TCartItem[];
  setDiscountValue: (v: number) => void;
  discountValue: number;
  paymentMethod: string;
  setOverallPrice: (v: number) => void;
  setSales_tag: (v: string) => void;
  sales_tag: string;
}

export const Summary = React.forwardRef<HTMLDivElement, SummaryProps>(
  (props, ref) => {
    const searchParams = useSearchParams();
    const {
      cart,
      setDiscountValue,
      discountValue,
      setOverallPrice,
      paymentMethod,
      setSales_tag,
      sales_tag
    } = props;
    const { active_cart, clear_cart } = useCartStore();
    const { company, branch, branchData } = useCompanyStore();
    const urlBatch_id = searchParams.get("batch_id") as string;
    const urlSales_tag = searchParams.get("sales_tag") as string;
    const [batchId, setBatchId] = React.useState("");
    const vatRate = branchData?.vat || 0;
    const chargesPercentage = branchData?.charges?.sales_charge || 0;
    // const chargesPercentage = 0.1 || 0;
    const chargeCapAmount = branchData?.charges?.sales_charge_cap || 0;
    // const chargeCapAmount = 1000;
    const isChargesPassedToCustomer =
      branchData?.transfer_charges_to_customer || false;
    const [additionalCharges, setAdditionalCharges] = React.useState(0);
    const { removeParamAndReload } = useChangeQueryParams();
    const [totalPrice, setTotalPrice] = React.useState(
      cart.reduce(
        (sum, item) => sum + Number(item.selling_price) * item.quantity,
        0
      )
    );
    const [subtotal, setSubtotal] = React.useState(
      cart.reduce(
        (sum, item) => sum + Number(item.selling_price) * item.quantity,
        0
      )
    );

    useEffect(() => {
      const cartTotalPrice = cart.reduce(
        (sum, item) => sum + Number(item.selling_price) * item.quantity,
        0
      );
      const newPrice = cartTotalPrice - discountValue;
      const calculateCharges = () => {
        if (!isChargesPassedToCustomer) return 0;
        const calculatedCharges = Math.min(
          cartTotalPrice * chargesPercentage,
          chargeCapAmount
        );
        if (
          paymentMethod === "TRANSFER" ||
          paymentMethod === "CARD" ||
          paymentMethod === "OTHER_TRANSFER"
        ) {
          setAdditionalCharges(Math.ceil(calculatedCharges));
          return Math.ceil(calculatedCharges);
        } else {
          setAdditionalCharges(0);
          return 0;
        }
      };
      const charges = calculateCharges();

      // Calculate the new total price based on the original price and charges
      const newTotalPrice = newPrice + charges;

      setTotalPrice(newTotalPrice);
      setOverallPrice(newTotalPrice);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [discountValue, cart, paymentMethod]);

    useEffect(() => {
      const cartTotalPrice = cart.reduce(
        (sum, item) => sum + Number(item.selling_price) * item.quantity,
        0
      );
      if (cartTotalPrice === 0) {
        setDiscountValue(0);
      }
      setSubtotal(cartTotalPrice);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cart]);
    useEffect(() => {
      if (urlBatch_id) {
        setBatchId(urlBatch_id);
      } else {
        setBatchId("");
      }
    }, [urlBatch_id]);
    useEffect(() => {
      if (urlSales_tag) {
        setSales_tag(urlSales_tag);
      } else {
        setSales_tag("");
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [urlSales_tag]);

    const actualTotal = () => {
      return totalPrice + totalPrice * (vatRate / 100);
    };
    const { mutate: save, isPending: isSavingSale } = UseSaveSale();
    const { mutate: UpdateSaveSales, isPending: isUpdatingSale } =
      UseUpdateSavedSale();

    const saveSale = () => {
      console.log(sales_tag);
      
      save(
        { company, branch, cart: active_cart, save: true, sales_tag },
        {
          onSuccess: () => {
            toast.success("Sale saved successfully");
            clear_cart();
            setSales_tag("")
          },
        }
      );
    };
    const handleUpdateSavedSales = () => {
      UpdateSaveSales(
        { company, branch, cart: active_cart, save: true, batch_id: batchId, sales_tag },
        {
          onSuccess: () => {
            toast.success("Saved Sale Updated successfully");
            clear_cart();
            setSales_tag("")
            removeParamAndReload("batch_id");
          },
        }
      );
    };

    return (
      <div
        className="bg-[#1C1C1C] divide-y-[1.2px] min-h-max shrink-0 divide-[#262729] p-4 rounded-xl border-2 border-transparent focus:border-blue-600 !outline-none overflow-y-scroll"
        ref={ref}
        tabIndex={0}
      >
        
        <Input
          className="border h-10 text-black mb-1"
          placeholder="Sales Tag e.g Table 1"
          type="text"
          onChange={(e) => {
            setSales_tag((e.target.value));
          }}
          value={sales_tag}
        />
        <div className="grid grid-cols-2 items-center gap-x-4 gap-y-2 mb-4">
          {/* <Button
            className="font-normal rounded-lg h-11 text-xs w-full"
            variant="secondary"
          >
            Update selling price
          </Button> */}
          {/* <Button className='font-normal rounded-lg h-11 text-xs' variant='secondary'>
                    Add customer
                </Button> */}
          <div>
            <Input
              className="border h-10 text-black"
              placeholder="Discount e.g ₦500"
              type="text"
              onChange={(e) => {
                if (
                  Number(e.target.value) < 0 ||
                  Number(e.target.value) > totalPrice
                ) {
                  toast.error("Invalid discount price");
                  setDiscountValue(0);
                } else {
                  setDiscountValue(Number(e.target.value));
                }
              }}
              onInput={(e: React.FormEvent<HTMLInputElement>) => {
                const target = e.target as HTMLInputElement;
                const value = target.value;
                target.value = value.replace(/[^0-9+]/g, "");
              }}
              value={discountValue == 0 ? "" : discountValue}
            />
          </div>
          {batchId ? (
            <Button
              className="font-normal rounded-lg h-11 text-xs px-8 w-full"
              variant="secondary"
              disabled={!cart.length}
              onClick={handleUpdateSavedSales}
            >
              {isUpdatingSale ? (
                <SmallSpinner className="ml-2" color="white" />
              ) : (
                "Update Saved Order"
              )}
            </Button>
          ) : (
            <Button
              className="font-normal rounded-lg h-11 text-xs px-8 w-full"
              variant="secondary"
              disabled={!cart.length}
              onClick={saveSale}
            >
              Save
              {isSavingSale && <SmallSpinner className="ml-2" color="white" />}
            </Button>
          )}
        </div>
        <h4 className="text-sm">Summary</h4>
        <table className="w-full">
          <tr className="flex items-center justify-between text-xs py-1.5">
            <td className="text-[#D8D8DF80]">Items</td>
            <td>{cart.length}</td>
          </tr>
          <tr className="flex items-center justify-between text-xs py-1.5">
            <td className="text-[#D8D8DF80]">Discount</td>
            <td>₦{discountValue}</td>
          </tr>
          <tr className="flex items-center justify-between text-xs py-1.5">
            <td className="text-[#D8D8DF80]">Charges</td>
            <td>₦{additionalCharges}</td>
          </tr>
          <tr className="flex items-center justify-between text-xs py-1.5">
            <td className="text-[#D8D8DF80]">Subtotal</td>
            <td>{convertNumberToNaira(subtotal)}</td>
          </tr>
          <tr className="flex items-center justify-between text-xs py-1.5">
            <td className="text-[#D8D8DF80]">Vat</td>
            <td>{vatRate}%</td>
          </tr>
          <tr className="flex items-center justify-between text-[0.975rem] py-2.5 border-t border-[#48494C] border-dashed">
            <td className="text-foreground text-[0.9rem]">Total</td>
            <td className="text-base">{convertNumberToNaira(actualTotal())}</td>
          </tr>
        </table>
      </div>
    );
  }
);

Summary.displayName = "Summary";
