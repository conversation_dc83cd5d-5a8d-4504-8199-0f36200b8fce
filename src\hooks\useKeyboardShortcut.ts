'use client'

import { useEffect } from 'react';

type KeyHandler = (event: KeyboardEvent) => void;

export const useKeyboardShortcut = (key: string, handler: KeyHandler, modifier?: string) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
        event.preventDefault();
      if (event.key === key && (!modifier || event[`${modifier}Key` as keyof KeyboardEvent])) {
        handler(event);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [key, handler, modifier]);
};
