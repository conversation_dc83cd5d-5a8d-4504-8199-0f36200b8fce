'use client'

import * as React from "react"
// import toast from "react-hot-toast"

import { <PERSON><PERSON>, ErrorModal, Dialog, DialogContent, DialogHeader, DialogClose, DialogTitle, Select, SelectTrigger, SelectItem, SelectContent, DialogDescription, Input, SingleDatePicker, FormError, DialogFooter, SelectValue, } from "@/components/ui"
// import { getInitials } from "@/utils/strings"
import { useErrorModalState } from "@/hooks"
import { TOrderMgtPipelineStageOrder } from "../types"
import toast from "react-hot-toast"
import { formatAxiosErrorMessage } from "@/utils/errors"
import { AxiosError } from "axios"
import { Controller, useForm } from "react-hook-form"
import { UseMarkOrderAsPaid } from "../api"
import { Label } from "@radix-ui/react-label"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { convertNumberToNaira } from "@/utils/currency"
import { SmallSpinner } from "@/components/icons"



interface props {
    setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
    isModalOpen: boolean
    refetchPipelineData: () => void
    currentOrder: TOrderMgtPipelineStageOrder
}



const schema = z.object({
    transaction_date: z.date({ required_error: "Transaction date is required" }),
    mode_of_transaction: z.enum(["CASH", "CARD", "USSD", "PAY_ON_DELIVERY"], { required_error: "Mode of Transaction is required" }),
    amount: z.number().min(1, { message: "Amount is required" }),
})

const OrderMgtPipelineStageOrderMarkAsPaidModal: React.FC<props> = ({ setModalOpen, isModalOpen, refetchPipelineData, currentOrder }) => {
    // const windowWidth = useWindowWidth()
    const {
        handleSubmit,
        formState: { errors }, control
    } = useForm<z.infer<typeof schema>>({
        resolver: zodResolver(schema),
    })

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const { mutate: markPaid, isPending: isMarkingAsPaid } = UseMarkOrderAsPaid()

    const SubmitForm = (data: z.infer<typeof schema>) => {
        markPaid({
            ...data,
            order: currentOrder.id,
        },
            {
                onSuccess() {
                    toast.success("Successfully marked order as paid")
                    refetchPipelineData()
                    setModalOpen(false)
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                },
            })
    }

    // {
    //     isMarkingAsPaid && <SmallSpinner className="ml-2" />
    // }

    return (
        <>
            <Dialog open={isModalOpen} onOpenChange={setModalOpen}>
                <DialogContent>
                    <DialogHeader className="bg-main-solid">
                        <DialogClose className="ml-auto bg-white text-red-900 px-4 py-2 text-sm rounded-full">
                            Close
                        </DialogClose>
                    </DialogHeader>

                    <div className="max-h-modal-body overflow-y-auto px-8 py-6 sm:max-h-none sm:overflow-y-visible">
                        <div className="px-6">
                            <DialogTitle className="font-heading text-xl p-0 m-0">
                                Record payment
                            </DialogTitle>
                            <DialogDescription className="mb-5">
                                Kindly note that order status will be updated
                                to ‘PAID’ on submitting this form
                            </DialogDescription>

                            <form className="space-y-5" onSubmit={handleSubmit(SubmitForm)} id="mark-as-read-form">
                                <div>
                                    <Controller
                                        control={control}
                                        name="mode_of_transaction"
                                        render={({ field: { onChange, value } }) => (
                                            <Select>
                                                <Label>Mode of transaction</Label>
                                                <SelectTrigger className="h-12 !m-0 text-sm">
                                                    <SelectValue placeholder="Select Payment Method" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem
                                                        onSelect={() => onChange("cash")}
                                                        className="flex items-center gap-2"
                                                        value="cash"
                                                    >
                                                        Cash
                                                    </SelectItem>
                                                    <SelectItem
                                                        onSelect={() => onChange("transfer")}
                                                        className="flex items-center gap-2"
                                                        value="transfer"
                                                    >
                                                        Transfer
                                                    </SelectItem>
                                                    <SelectItem
                                                        onSelect={() => onChange("pos")}
                                                        className="flex items-center gap-2"
                                                        value="pos"
                                                    >
                                                        POS
                                                    </SelectItem>
                                                    <SelectItem
                                                        onSelect={() => onChange("cheque")}
                                                        className="flex items-center gap-2"
                                                        value="cheque"
                                                    >
                                                        Cheque
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        )}

                                    />

                                    {
                                        errors.mode_of_transaction && (
                                            <FormError errorMessage={errors.mode_of_transaction.message} />
                                        )
                                    }
                                </div>

                                <div>
                                    <Label className="text-sm">Amount</Label>
                                    <p
                                        className="flex items-center p-4 w-full rounded-md !bg-black focus:bg-black focus:outline-none border !border-white h-12 appearance-none text-sm"
                                    >
                                        {convertNumberToNaira(currentOrder?.total_price)}
                                    </p>
                                </div>
                                <div className="col-span-2 flex w-full grid-cols-[subgrid] flex-col lg:grid ">
                                    <Label
                                        className="block text-[0.875rem] font-semibold leading-[27px] text-label-text"
                                        htmlFor="transaction_date"
                                    >
                                        Transaction date
                                    </Label>
                                    <div className="w-full max-w-[550px]">
                                        <Controller
                                            control={control}
                                            name="transaction_date"
                                            render={({ field: { onChange, value } }) => (
                                                <SingleDatePicker
                                                    className="flex h-12 w-full rounded-md border-[1.65px] border-white bg-transparent px-5 py-2 text-xs text-white transition duration-300 file:border-0 placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                                    id="transaction_date"
                                                    placeholder="Date of transaction"
                                                    value={value}
                                                    onChange={onChange}
                                                />
                                            )}
                                        />
                                    </div>

                                    {errors?.transaction_date && (
                                        <FormError errorMessage={errors.transaction_date.message} />
                                    )}
                                </div>
                            </form>
                        </div>


                    </div>
                    <DialogFooter className="p-5">
                        <Button className="w-full h-14" type="submit" form="mark-as-read-form" >
                            Save record
                            {
                                isMarkingAsPaid && <SmallSpinner className="ml-2" />
                            }
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>


            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'An Error Occured.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </>

    )
}


export default OrderMgtPipelineStageOrderMarkAsPaidModal