import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={28}
    height={24}
    viewBox="0 0 28 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={27.2} height={24} rx={6} fill="#142D22" />
    <path
      d="M16.9166 17.3952H11.0833C8.95413 17.3952 7.72913 16.1702 7.72913 14.041V9.95768C7.72913 7.82852 8.95413 6.60352 11.0833 6.60352H16.9166C19.0458 6.60352 20.2708 7.82852 20.2708 9.95768V14.041C20.2708 16.1702 19.0458 17.3952 16.9166 17.3952ZM11.0833 7.47852C9.41496 7.47852 8.60413 8.28935 8.60413 9.95768V14.041C8.60413 15.7093 9.41496 16.5202 11.0833 16.5202H16.9166C18.585 16.5202 19.3958 15.7093 19.3958 14.041V9.95768C19.3958 8.28935 18.585 7.47852 16.9166 7.47852H11.0833Z"
      //@ts-ignore
      fill={"currentColor" || "#12B669"}
    />
    <path
      d="M14 14.1875C12.7925 14.1875 11.8125 13.2075 11.8125 12C11.8125 10.7925 12.7925 9.8125 14 9.8125C15.2075 9.8125 16.1875 10.7925 16.1875 12C16.1875 13.2075 15.2075 14.1875 14 14.1875ZM14 10.6875C13.2767 10.6875 12.6875 11.2767 12.6875 12C12.6875 12.7233 13.2767 13.3125 14 13.3125C14.7233 13.3125 15.3125 12.7233 15.3125 12C15.3125 11.2767 14.7233 10.6875 14 10.6875Z"
      //@ts-ignore
      fill={"currentColor" || "#12B669"}
    />
    <path
      d="M10.2084 13.8952C9.96921 13.8952 9.77087 13.6968 9.77087 13.4577V10.541C9.77087 10.3018 9.96921 10.1035 10.2084 10.1035C10.4475 10.1035 10.6459 10.3018 10.6459 10.541V13.4577C10.6459 13.6968 10.4475 13.8952 10.2084 13.8952Z"
      //@ts-ignore
      fill={"currentColor" || "#12B669"}
    />
    <path
      d="M17.7916 13.8952C17.5525 13.8952 17.3541 13.6968 17.3541 13.4577V10.541C17.3541 10.3018 17.5525 10.1035 17.7916 10.1035C18.0308 10.1035 18.2291 10.3018 18.2291 10.541V13.4577C18.2291 13.6968 18.0308 13.8952 17.7916 13.8952Z"
      //@ts-ignore
      fill={"currentColor" || "#12B669"}
    />
  </svg>
);
export default SVGComponent;
