import { useMutation } from '@tanstack/react-query';
import { managementAxios } from '@/lib/axios';



export type moveOrdertoNextStageDTO = {
    pipeline_id: string;
    new_stage_id: string;
    order_ids: string[];
}

const moveOrdertoNextStage = async (moveOrdertoNextStageDto: moveOrdertoNextStageDTO) => {
    const response = await managementAxios.post(
        '/orders/progression/',
        moveOrdertoNextStageDto,
    );
    return response.data
};

export const UseMoveOrdertoNextStage = () => {
    return useMutation({
        mutationFn: moveOrdertoNextStage,
       
    });
};
