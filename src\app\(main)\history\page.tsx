'use client'

import { CameraIcon, LogoutIcon, Paybox360, ScanIcon } from '@/components/icons';
import { Button, LinkButton } from '@/components/ui';
import { useCompanyStore } from '@/stores/company';
import { PaginationState } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Eye } from 'lucide-react';
import React from 'react';
import { SavedSalesTable, SuccessfulSalesTable } from './misc/components';
import AppHeader from '@/components/layout/BaseAppHeader';
import { convertToTitleCase } from '@/utils/strings';

export default function History() {
    const [searchTerm, setSearchTerm] = React.useState<string>('')
    const { first_name, last_name, company, branch } = useCompanyStore()
    const userName = first_name + last_name


    const [{ pageIndex, pageSize }, setPagination] =
        React.useState<PaginationState>({
            pageIndex: 0,
            pageSize: 10,
        });

    // const fetchOptions = {
    //     company,
    //     branch,
    //     pageSize: pageSize,
    //     pageIndex: pageIndex,
    //     search: searchTerm
    // }

    // const { data, isLoading, isFetching, refetch } = UseGetSales(fetchOptions)
    return (
        <div className='grid grid-rows-[max-content,1fr] flex-col w-screen h-screen '>
           <AppHeader
                showSearch={false}
                activeTab='sales'
                userName={`${convertToTitleCase(first_name)} ${convertToTitleCase(last_name)}`}
                className='sticky top-0 inset-x-0'
            />

            <div className='flex flex-col lg:grid grid-cols-2 px-6 py-4 gap-6 h-[calc(100vh-8.5vh)]'>
                <article className='size-full rounded-lg bg-[#1C1C1C] px-8 pb-5 overflow-y-scroll'>
                    <SuccessfulSalesTable />
                </article>
                <article className='size-full rounded-lg bg-[#1C1C1C] px-8 pb-5 overflow-y-scroll'>
                    <SavedSalesTable company={company} branch={branch} />
                </article>
            </div>
        </div>
    )
}

