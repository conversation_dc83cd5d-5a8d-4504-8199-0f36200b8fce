'use client';

import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { format as formatDate, parseISO, subMonths } from 'date-fns';
import React from 'react';
import { DateRange } from 'react-day-picker';
import { useForm, useWatch } from 'react-hook-form';

import { Button, DataTable2 } from '@/components/ui';
import { useDebounce } from '@/hooks';

import { TOrderMgtBranchCustomers } from '../../types';
import { SmallSpinner } from '@/components/icons';
import { useGetOrderCustomers } from '../../api/getOrderMgtCustomers';
// import { SmallSpinner } from '@/icons/core';

const today = new Date();
const oneMonthAgo = subMonths(new Date(), 1);

interface OrdersCustomersTableProps {
  branchId: string;
}

export default function OrdersCustomersTable({
  branchId,
}: OrdersCustomersTableProps) {

  const columns: ColumnDef<TOrderMgtBranchCustomers>[] = [
    {
      accessorKey: 'id',
      header: 'S/N',
      cell: ({ row }) => {
        return <span>{row.index + 1}</span>;
      },
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const name = String(row.getValue('name'));
        return <span className="text-sm text-muted-foreground first-letter:capitalize">{name}</span>;
      },
    },

    {
      accessorKey: 'email',
      header: 'Email address',
      cell: ({ row }) => {
        const name = String(row.getValue('email'));
        return <span className="text-sm text-muted-foreground first-letter:lowercase">{name ?? ''}</span>;
      },
    },
    {
      accessorKey: 'phone_number',
      header: 'Phone Number',
      cell: ({ row }) => {
        const phone = String(row.getValue('phone_number'));
        return <span className="text-sm text-muted-foreground">{phone}</span>;
      },
    },
    {
      accessorKey: 'total_orders',
      header: 'Total Orders',
      cell: ({ row }) => {
        const total_orders = row.getValue('total_orders') as number;
        return <span className="text-sm text-muted-foreground">{total_orders}</span>;
      },
    },
    {
      accessorKey: 'last_order',
      header: 'Last Order Date',
      cell: ({ row }) => {
        const date = parseISO(row.original.last_order);
        const formatted = formatDate(date, 'd/M/yyyy - hh:mma');

        return <span className="text-sm text-muted-foreground first-letter:">{formatted}</span>;
      },
    },



    {
      id: 'actions',
      header: 'Action',
      cell: ({ }) => {
        return (
          <div className="flex items-center justify-between gap-x-4">
            <Button
              className="inline-block whitespace-nowrap px-3"
              // variant={'light'}
            >
              View Details
            </Button>
          </div>
        );
      },
    },
  ];

  const { control, register } = useForm<{
    searchFilter: string;
    dateFilter: DateRange;
  }>({
    defaultValues: {
      searchFilter: '',
      dateFilter: {
        from: oneMonthAgo,
        to: today,
      },
    },
  });

  const { searchFilter, dateFilter } = useWatch({ control });
  const debouncedSearchFilter = useDebounce(searchFilter, 500);

  const { from, to } = dateFilter || {};
  const startDate = from ? formatDate(from, 'yyyy-MM-dd') : '';
  const endDate = to ? formatDate(to, 'yyyy-MM-dd') : '';

  const [{ pageIndex, pageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: 1,
      pageSize: 100,
    });

  const fetchOptions = {
    pageIndex,
    pageSize,
    startDate,
    endDate,
    search: debouncedSearchFilter,
    branchId,
  };


  const { data: customersData, isLoading, isFetching } = useGetOrderCustomers(fetchOptions)

  const { results: rows, count: total_customer_count } = customersData || {};
  const pageCount = React.useMemo(
    () => (!!total_customer_count ? Math.ceil(total_customer_count / pageSize) : -1),
    [total_customer_count, pageSize]
  );




  return (
    <div>
      <section className="mt-1 flex flex-wrap items-stretch justify-between gap-4 rounded-10  p-6 lg:py-5 bg-background">
        <p className='text-sm'>
          Customers:
          <span className='inline-block px-4 py-2 ml-1.5 rounded-md bg-main-bg '>
            {
              total_customer_count ??
              <SmallSpinner color="white" />
            }
          </span>
        </p>

        <div className="hidden max-w-[17.1875rem] items-center gap-2.5 rounded-lg border border-[#d6d6d6]/50 px-4 transition duration-300 ease-in-out focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2">

          <input
            autoCapitalize="none"
            autoComplete="off"
            autoCorrect="off"
            className="grow px-0 py-3 text-sm ring-0 placeholder:text-white/50 focus-visible:outline-none"
            id="team_name"
            placeholder="Search"
            type="text"
            {...register('searchFilter')}
          />
        </div>

      </section>

      <DataTable2
        columns={columns}
        isFetching={isFetching}
        isLoading={isLoading}
        pageCount={pageCount}
        pageIndex={pageIndex}
        pageSize={pageSize}
        rows={rows}
        setPagination={setPagination}
      />
    </div>
  );
}
