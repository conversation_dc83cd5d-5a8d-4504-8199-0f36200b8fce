import { useQuery } from '@tanstack/react-query';

import { managementAxios } from "@/lib/axios";

export interface SalesTransactionResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    sales_transactions: ISalesTransactionDto[];
    count: number;
    total_transactions: number;
    total_sales: number;
  };
  errors: null;
}


export interface ISalesTransactionDto {
  id: string
  created_at: string
  updated_at: string
  offline_identifier: any
  batch_id: string
  sales_tag: string
  status: string
  means_of_payment: any
  total_sales_amount: string
  discount_value: string
  delivery_fee: string
  vat_amount: string
  total_cost: string
  amount_paid: string
  paid: boolean
  paid_at: any
  card_rrn: any
  device: string
  rrn_register_payload: any
  company: string
  branch: string
  customer: string
  invoice: string
  created_by: string
  updated_by: any
  sales: Sale[]
}

export interface Sale {
  created_at: string
  batch_id: string
  status: string
  category_id: string
  item_id: string
  item_description: string
  quantity: number
  reference: string
  amount: number
  sub_total: number
  discount: number
  delivery_fee: number
  vat: number
  total: number
}


export type SingleSaleHistory = {
  company: string;
  created_at: string;
  branch: string;
  cashier?: string;
  amount: number;
  payment_method: string;
  transaction_id: string;
  invoice?: string;
  status: string;
};

export type SalesTransactionDto = {
  id: string;
  created_at: string;
  updated_at: string;
  batch_id: string;
  status: string;
  means_of_payment: string | null;
  total_sales_amount: string;
  discount_value: string;
  delivery_fee: string;
  vat_amount: string;
  total_cost: string;
  amount_paid: string;
  paid: boolean;
  paid_at: string | null;
  device: string;
  company: string;
  branch: string;
  customer: string;
  invoice: string;
  created_by: string;
  updated_by: string | null;
};

interface FetchTeamsOptions {
  companyId: string;
  branchId: string;
  pageIndex?: number;
  pageSize?: number;
  startDate?: string | undefined;
  endDate?: string | undefined;
  search?: string;
}

export const getPendingOrders = async ({
  companyId,
  branchId,
  pageIndex,
  pageSize,
  startDate,
  endDate,
  search,
}: FetchTeamsOptions): Promise<SalesTransactionResponse> => {
  const response = await managementAxios.get(
    `/api/v1/sales/register/?company=${companyId}&branch=${branchId}&transaction_status=saved&page=${pageIndex}&page_size=${pageSize}&timestamp_gte=${startDate}&timestamp_lte=${endDate}&search=${search}&device=WEB_POS`
  );
  return response.data; //as StockTableResponse;
};

export const useGetPendingOrders = (fetchOptions: FetchTeamsOptions) => {
  return useQuery({
    queryKey: ['pending-orders-table', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getPendingOrders(fetchOptions);
    },
    enabled: !!fetchOptions,
  });
};