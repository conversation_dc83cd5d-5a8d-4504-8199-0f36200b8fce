import { <PERSON><PERSON><PERSON><PERSON>, TwoTonedCopyIcon } from '@/components/icons';
import { <PERSON>ton, <PERSON>alog, DialogContent, DialogFooter, DialogHeader, Input, Popover, PopoverContent, PopoverTrigger, Skeleton } from '@/components/ui';
import { useCompanyStore } from '@/stores';
import { TBranch } from '@/types/user';
import { cn } from '@/utils/classNames';
import { useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Banknote, SaveIcon, Trash2Icon, XIcon } from 'lucide-react';
import React, { ChangeEvent, useState } from 'react';
import toast from 'react-hot-toast';
import { UseGetInstantAccount } from '../api';
import { useGetCustomers } from '../api/getCustomers';
import { useAddCustomer } from '../api/postAddCustomer';
import { useAddLocation } from '../api/postAddLocation';
import { T<PERSON><PERSON>Item, TCustomer, TNewCustomer } from '../types';
import { useBooleanStateControl } from '@/hooks';
import { CheckBrowserCompatibility } from './BrowserVersion/CheckBrowserCompatibility';
import { RegisterSalesData, TSalesConfirmationResult } from '../api/patchConfirmSale';
import { PrintReceiptFnProps } from './ReceiptPrintButton';

interface Props {
  isModalOpen: boolean;
  closeModal: () => void
  confirmFunction: () => void
  isConfirming: boolean
  amount: string
  batchId: string
  paymentMethod?: string;
  company?: string;
  branchData?: TBranch;
  selectedCustomer?: TCustomer;
  items?: TCartItem[];
  setSelectedCustomer: (selectedCustomer: TCustomer | undefined) => void;
  salesConfirmationResult?: TSalesConfirmationResult
  registerSalesResponse?: RegisterSalesData;
  printReceipt: () => void
}

type bankData = {
  bank_name: string;
  bank_code: string;
  account_name: string;
  account_number: string;
}

const ConfirmPaymentTransferModal: React.FC<Props> = ({ isModalOpen, closeModal, amount, confirmFunction, isConfirming, batchId, branchData, selectedCustomer, setSelectedCustomer, items, paymentMethod, salesConfirmationResult, printReceipt }) => {
  const {
    state: isBrowserCompartibilityModalOpen,
    setTrue: openBrowserCompartibilityModal,
    setFalse: closeBrowserCompartibilityModal
  } = useBooleanStateControl();

  const [accountDetails, setAccountDetails] = useState({
    bank_name: '',
    bank_code: '',
    account_name: '',
    account_number: ''
  });
  const [search, setSearch] = useState<string>('')
  const [addresses, setAddresses] = useState<string[]>([]);
  const [newCustomer, setNewCustomer] = useState<TNewCustomer>();
  const [isFocused, setIsFocused] = useState(false);

  const queryClient = useQueryClient();

  const { company, branch } = useCompanyStore()

  const { mutate: getInstantAccount, isPending: isGettingAccountDetails } = UseGetInstantAccount()
  const { data: customersResponse, isLoading: isLoadingCustomers } = useGetCustomers(company as string, branch as string, search);
  const { mutate: addLocation, isPending: isAddingLocation } = useAddLocation();
  const { mutate: addNewCustomer, isPending: isAddingCustomer } = useAddCustomer();


  React.useEffect(() => {
    if (isModalOpen) {

      getInstantAccount({
        company,
        branch,
        batch_id: batchId
      }, {
        onSuccess: (data) => {
          setAccountDetails(data.data)
        }
      })
    }
  }, [isModalOpen]);


  const buttonRef = React.useRef<HTMLButtonElement>(null);
  React.useEffect(() => {
    if (isModalOpen) {
      buttonRef.current?.focus();
    }
    buttonRef.current?.focus();
  }, [isModalOpen]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape" || event.key === "Backspace") {
      closeModal();
    }
    if (event.key === "Enter") {
      event.preventDefault();
      confirmFunction();
    }
  };

  React.useEffect(() => {
    const detectChromeVersion = () => {
      const userAgent = navigator.userAgent;
      const chromeRegex = /Chrome\/(\d+)\./;
      const match = userAgent.match(chromeRegex);

      if (match && match[1]) {
        const version = parseInt(match[1], 10);
        setChromeVersion(version);
        // Chrome 97 was released in January 2022
        setIsOldVersion(version <= 97);
      } else {
        setChromeVersion(null);
        setIsOldVersion(null);
      }
    };

    detectChromeVersion();
  }, []);


  const [chromeVersion, setChromeVersion] = React.useState<number | null>(null);
  const [isOldVersion, setIsOldVersion] = React.useState<boolean | null>(null);


  const copyToClipboard = (string: string) => {
    navigator.clipboard.writeText(string)
    toast.success('Copied to clipboard')
  }

  function removeAddress(index: number): void {
    setAddresses(prevArray => prevArray.filter((_, i) => i !== index));
  }


  const customers = customersResponse?.data.customers

  function handleSearch(value: string): void {
    setSearch(value)
  }

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setIsFocused(false);
    }, 300);
  };

  function handleNewCustomerChange(event: ChangeEvent<HTMLInputElement>): void {
    setNewCustomer({ ...newCustomer as TCustomer, [event.target.name]: event.target.value })
  }

  function addAddress(index: number, customer: TCustomer): void {
    const location = addresses[index]
    addLocation({
      customer: customer.id,
      location,
      default: false
    }, {
      onSuccess() {
        toast.success('Address added successfully');
        queryClient.invalidateQueries({
          queryKey: ['customers-list'],
        });
        setAddresses(prevArray => prevArray.filter((_, i) => i !== index));
      }
    })
  }

  function addCustomer(customer: TNewCustomer): void {
    addNewCustomer(customer, {
      onSuccess({ data }) {
        toast.success('Customer added successfully');
        queryClient.invalidateQueries({
          queryKey: ['customers-list'],
        });
        setSelectedCustomer(data || undefined);
        setNewCustomer(undefined);
        // Refetch the 'customers-list' query to get the updated data
        // queryClient.refetchQueries({ queryKey: ['customers-list'] }).then(() => {
        //   const customersList = queryClient.getQueryData<TCustomer[]>(['customers-list']);
        //   // Find the newly added customer by ID
        //   const newCustomer = customersList?.find(c => c.id === data.id);
        //   console.log(newCustomer);
          
        // });
      }
    })
  }
  const isButtonDisabled = () => {
    const { company, branch, name, address, email, phone } = newCustomer as TNewCustomer;
    return !company || !branch || !name || !address || (!email && !phone);
};

  function convertToTitleCase(str: string): string {
    return str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  }

  function getEscPosCommands(): Uint8Array {


    const currentDate = new Date();
    const formattedDate = format(currentDate, 'dd/MM/yyyy');
    const formattedTime = format(currentDate, 'H:mm:ss aa');
    const orderId = batchId; //Math.floor(1000 + Math.random() * 9000);
    const total = items?.reduce((acc, item) => acc + parseInt(item.selling_price) * item.quantity, 0);

    const encoder = new TextEncoder();
    const ESC = 0x1B;
    const GS = 0x1D;
    const commands: number[] = [
      // Initialize printer
      ESC, 0x40,

      // Center align
      ESC, 0x61, 0x01,

      // Company name (double-width, double-height)
      //@ts-ignore
      ESC, 0x21, 0x30, ...encoder.encode(branchData?.company + '\n'),
      // Reset text size
      ESC, 0x21, 0x00,

      // Address and support info
      //@ts-ignore
      ...encoder.encode(branchData?.address + '\n'),
      //@ts-ignore
      ...encoder.encode((branchData?.phone ?? "no phone number") + '\n\n'),

      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Payment method
      //@ts-ignore
      ...encoder.encode(convertToTitleCase(paymentMethod) + ' Payment\n'),

      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Left align
      ESC, 0x61, 0x00,

      // Order info
      //@ts-ignore
      ...encoder.encode(`Order ID: #${orderId}  \n  Date: ${formattedDate}\n`),
      //@ts-ignore
      ...encoder.encode(`Cashier: ${branchData?.name || 'Staff'}  \n  Time: ${formattedTime}\n\n`),
      //@ts-ignore
      ...encoder.encode(`Branch phone: ${branchData?.phone || ''}\n\n`),

      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),




      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Left align
      ESC, 0x61, 0x00,

      // Order info
      //@ts-ignore
      ...encoder.encode(`Name:${selectedCustomer?.name || 'No customer name'}\n Phone: ${selectedCustomer?.phone || 'No customer phone'}\n`),
      //@ts-ignore
      ...encoder.encode(`Address: ${selectedCustomer?.address || 'No customer address'}  \n\n`),

      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),



      // Items header
      //@ts-ignore
      ...encoder.encode('Item name         Qty     Amount\n'),
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Items
      //@ts-ignore
      ...salesConfirmationResult?.items?.flatMap(item => [
        //@ts-ignore
        ...encoder.encode(
          `${(item?.item_description || 'Unknown Item').padEnd(15).slice(0, 15)}` +
          `${(item?.quantity || 0).toString()}     ` +
          `N${(parseInt(item?.amount) || 0)}\n`
        )
      ]),
      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Total
      //@ts-ignore
      ...encoder.encode(`Subtotal: N${registerSalesResponse?.sub_total.toFixed(2)}\n`),
      // Charges
      //@ts-ignore
      // ...encoder.encode(`Charges: N${registerSalesResponse?.charges.toFixed(2) || 0}\n`),
      // Vat
      //@ts-ignore
      ...encoder.encode(`VAT: N${registerSalesResponse?.vat.toFixed(2) || 0}\n`),
      // Discount
      //@ts-ignore
      ...encoder.encode(`Discount: N${registerSalesResponse?.discount_value || 0}\n`),
      // Total
      //@ts-ignore
      ...encoder.encode(`Total: N${registerSalesResponse?.total || 0}\n`),

      // Separator line
      //@ts-ignore
      ...encoder.encode('--------------------------------\n'),

      // Center align
      ESC, 0x61, 0x01,

      // Thank you message
      //@ts-ignore
      ...encoder.encode('Thank you for your patronage!\n'),
      //@ts-ignore
      ...encoder.encode('For feedback and enquiries \ncontact \n'),
      //@ts-ignore
      ...encoder.encode(`${branchData?.company || '<EMAIL>'}\n`),
      //@ts-ignore
      ...encoder.encode(`${branchData?.user || '00000000000'}\n\n`),

      // Feed and cut paper
      GS, 0x56, 0x00
    ];

    return new Uint8Array(commands);
  }

  //@ts-ignore
  async function sendDataInChunks(characteristic: BluetoothRemoteGATTCharacteristic, data: Uint8Array) {
    const CHUNK_SIZE = 120
    let offset = 0;

    while (offset < data.length) {
      const chunk = data.slice(offset, offset + CHUNK_SIZE);
      try {
        await characteristic.writeValue(chunk);
        offset += CHUNK_SIZE;
      } catch (error) {
        console.error('Failed to write data chunk:', error);
        throw error;
      }
    }
  }
  //@ts-ignore
  async function checkAndPrint(device: BluetoothDevice) {
    try {
      const server = await device.gatt!.connect();
      console.log(`Connected to GATT server`);
      const service = await server.getPrimaryService('000018f0-0000-1000-8000-00805f9b34fb');
      const characteristic = await service.getCharacteristic('00002af1-0000-1000-8000-00805f9b34fb');

      if (characteristic) {
        const printData = getEscPosCommands();
        await sendDataInChunks(characteristic, printData);
        console.log('Data sent to characteristic.');
      }
    } catch (error) {
      console.error('Bluetooth printing error:', error);
    }
  }

  async function handlePaymentConfirmation() {
    if (Number(chromeVersion) <= 97) {
      openBrowserCompartibilityModal()
    } else {
      try {
        //@ts-ignore
        const device = await navigator?.bluetooth?.requestDevice({
          acceptAllDevices: true,
          optionalServices: [
            '000018f0-0000-1000-8000-00805f9b34fb' // Adjust service UUID if needed {OMO might need to start generatimg UUID but for now we use this
          ]
        });

        console.log(`Connected to device: ${device.name}`);

        if (!device.gatt) {
          throw new Error('GATT is not available on this device.');
        }

        await checkAndPrint(device);
      } catch (error) {
        console.error('Error interacting with Bluetooth device:', error);
      }
    }
  }


  return (
    <Dialog modal={true} open={isModalOpen} >
      <DialogContent
        aria-label={"Confirm Cash Payment"}
        className={cn(
          'rounded-[10px]',
          'my-6 bg-[#1C1C1C] !px-0 !pb-0 !pt-4 !max-w-[500px]'
        )}
        style={{
          width: '92.5%',
          minWidth: '300px',
        }}
        onPointerDownOutside={closeModal}
        onKeyDown={handleKeyDown}
      >

        <DialogHeader className={cn('max-sm:sticky top-0 z-10 px-6 md:px-8')}>
          <div className='flex items-center justify-between'>
            <h5 className='text-base font-medium '>Transfer</h5>
            <Button className='bg-[#D9D9D91A] p-0  rounded-full h-9 w-9' size={'sm'} variant={'unstyled'} onClick={closeModal}>
              <XIcon size={20} />
            </Button>
          </div>
        </DialogHeader>



        <div className={cn('flex flex-col gap-3 p-6 bg-[#D9D9D91A] rounded-xl mx-6 md:mx-8')}>
          <p className='flex items-center gap-1.5 text-sm text-[#A2A2A2]'>
            <span className='flex items-center justify-center h-6 w-6 rounded-full bg-white'>
              <Banknote className='text-black' size={16} />
            </span>
            Transfer payment
          </p>
          <p className='text-xl font-medium'>
            {amount}
          </p>

          <p className='p-0 text-white/80 text-xs'>
            Transfer amount shown above to complete this transaction.
          </p>
        </div>

        <p className='flex items-center text-sm text-[#A2A2A2] px-8'>
          Search to select or add customer details
        </p>

        <div className='px-8 space-y-1'>
          <Input
            className="h-[2.5rem] p-6"
            type="text"
            placeholder="Enter phone number to search or add"
            // value={inputValue}
            onChange={(e) => handleSearch(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            rightIcon={
              (search && !customers?.length && !isLoadingCustomers) &&
              <Button
                variant={'unstyled'}
                className='mt-[-25%] mr-[-4]'
                onClick={() => {
                  setSelectedCustomer(undefined)
                  setNewCustomer({
                    company: company as string,
                    branch: branchData?.id as string,
                    name: '',
                    email: '',
                    phone: search,
                    address: '',
                  })
                }}
              >
                +
              </Button>
            }
          />
          {customers?.length && isFocused ?
            <div className='bg-[#313131] rounded-lg max-h-80 overflow-scroll absolute w-[calc(100%-4rem)] z-10'>
              {customers.map((customer) => (
                <div
                  key={customer.id}
                  className="p-4 relative cursor-pointer"
                  onClick={() => {
                    setNewCustomer(undefined)
                    setSelectedCustomer(customer)
                  }}
                >
                  <div>{customer.phone} . {customer.name}</div>
                  <div>{customer.address}</div>
                  <div className='absolute text-white top-3 right-5 text-xl'>+</div>
                </div>
              ))}
            </div> : null
          }
          {newCustomer?.phone &&
            <div className="p-4 relative bg-[#D9D9D91A] rounded-lg mb-4 mt-5 space-y-2">
              <div>
                <div className='text-[#696969]'>Name</div>
                <Input name='name' onChange={handleNewCustomerChange} value={newCustomer?.name} type="text" />
              </div>
              <div className="grid grid-cols-2 mb-3 gap-1">
                <div>
                  <div className='text-[#696969]'>Phone no</div>
                  <Input name='phone' onChange={handleNewCustomerChange} value={newCustomer?.phone} type="text" />
                </div>
                <div>
                  <div className='text-[#696969]'>Email</div>
                  <Input name='email' onChange={handleNewCustomerChange} value={newCustomer?.email} type="text" />
                </div>
              </div>
              <div>
                <div className='text-[#696969]'>Address</div>
                <Input name='address' onChange={handleNewCustomerChange} value={newCustomer?.address} type="text" />
              </div>
              <Button
                disabled={isButtonDisabled()}
                onClick={() => addCustomer(newCustomer)}
              >
                {isAddingCustomer ? <SmallSpinner /> : "Save"}
              </Button>
            </div>
          }
          {selectedCustomer ?
            <div>
              <div className='text-[#696969] mt-3 mb-1'>Tap edit icon to update details</div>
              <div className="p-4 relative bg-[#D9D9D91A] rounded-lg mb-4">
                <div className="grid grid-cols-2 mb-3">
                  <div>
                    <div className='text-[#696969]'>Phone no</div>
                    <div>{selectedCustomer.phone}</div>
                  </div>
                  <div>
                    <div className='text-[#696969]'>Name</div>
                    <div>{selectedCustomer.name}</div>
                  </div>
                </div>
                <div>
                  <div className='text-[#696969]'>Address</div>
                  <div>{selectedCustomer.address}</div>
                </div>
                <div className='absolute top-3 right-5 text-xl'>
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.25 17.0625H6.75C2.6775 17.0625 0.9375 15.3225 0.9375 11.25V6.75C0.9375 2.6775 2.6775 0.9375 6.75 0.9375H8.25C8.5575 0.9375 8.8125 1.1925 8.8125 1.5C8.8125 1.8075 8.5575 2.0625 8.25 2.0625H6.75C3.2925 2.0625 2.0625 3.2925 2.0625 6.75V11.25C2.0625 14.7075 3.2925 15.9375 6.75 15.9375H11.25C14.7075 15.9375 15.9375 14.7075 15.9375 11.25V9.75C15.9375 9.4425 16.1925 9.1875 16.5 9.1875C16.8075 9.1875 17.0625 9.4425 17.0625 9.75V11.25C17.0625 15.3225 15.3225 17.0625 11.25 17.0625Z" fill="white" />
                    <path d="M6.375 13.2674C5.9175 13.2674 5.4975 13.1024 5.19 12.8024C4.8225 12.4349 4.665 11.9024 4.7475 11.3399L5.07 9.08242C5.13 8.64742 5.415 8.08492 5.7225 7.77742L11.6325 1.86742C13.125 0.374922 14.64 0.374922 16.1325 1.86742C16.95 2.68492 17.3175 3.51742 17.2425 4.34992C17.175 5.02492 16.815 5.68492 16.1325 6.35992L10.2225 12.2699C9.915 12.5774 9.3525 12.8624 8.9175 12.9224L6.66 13.2449C6.5625 13.2674 6.465 13.2674 6.375 13.2674ZM12.4275 2.66242L6.5175 8.57242C6.375 8.71492 6.21 9.04492 6.18 9.23992L5.8575 11.4974C5.8275 11.7149 5.8725 11.8949 5.985 12.0074C6.0975 12.1199 6.2775 12.1649 6.495 12.1349L8.7525 11.8124C8.9475 11.7824 9.285 11.6174 9.42 11.4749L15.33 5.56492C15.8175 5.07742 16.0725 4.64242 16.11 4.23742C16.155 3.74992 15.9 3.23242 15.33 2.65492C14.13 1.45492 13.305 1.79242 12.4275 2.66242Z" fill="white" />
                    <path d="M14.8877 7.3727C14.8352 7.3727 14.7827 7.3652 14.7377 7.3502C12.7652 6.7952 11.1977 5.2277 10.6427 3.2552C10.5602 2.9552 10.7327 2.6477 11.0327 2.5577C11.3327 2.4752 11.6402 2.6477 11.7227 2.9477C12.1727 4.5452 13.4402 5.8127 15.0377 6.2627C15.3377 6.3452 15.5102 6.6602 15.4277 6.9602C15.3602 7.2152 15.1352 7.3727 14.8877 7.3727Z" fill="white" />
                  </svg>
                </div>
              </div>
              {addresses.map((address, index) => (
                <div key={index} className="relative">
                  <textarea
                    className="w-full min-h-16 p-4 pt-9 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 bg-[#D9D9D91A]"
                    placeholder="Enter full address"
                    value={address}
                    onChange={(e) => {
                      setAddresses((prev) => {
                        const newArray = [...prev];
                        newArray[index] = e.target.value;
                        return newArray;
                      })
                    }}
                  ></textarea>
                  <Button
                    variant={'unstyled'}
                    className="absolute top-1 right-[56px] text-gray-500 hover:text-blue-500 cursor-pointer p-0 h-6"
                    disabled={!address}
                    onClick={() => addAddress(index, selectedCustomer)}
                  >
                    <SaveIcon />
                  </Button>
                  <Button
                    variant={'unstyled'}
                    className="absolute top-1 right-3 text-gray-500 hover:text-red-500 cursor-pointer p-0 h-6"
                    onClick={() => removeAddress(index)}
                  >
                    <Trash2Icon />
                  </Button>
                </div>
              ))}
              <Button
                className='p-0 h-full mt-4'
                variant={'ghost'}
                onClick={() => {
                  setAddresses([...addresses, '']);
                }}
              >
                Tap add new address <span className='text-xl ml-3'>+</span>
              </Button>
            </div> : null
          }
        </div>


        <div className={cn('flex flex-col gap-3 p-6 bg-[#D9D9D91A] rounded-xl mx-6 md:mx-8')}>
          <h3>
            Bank account details
          </h3>

          <div className='space-y-4'>
            <div>
              <p className='text-[0.825rem] text-[#A2A2A2]'>
                Account Name
              </p>

              <p className='flex items-center justify-between'>
                {
                  isGettingAccountDetails ?
                    <Skeleton className='w-[150px] h-5 rounded-xl' />
                    :
                    accountDetails.account_name
                }
                <button onClick={() => copyToClipboard(accountDetails.account_name)} disabled={isGettingAccountDetails}>
                  <TwoTonedCopyIcon />
                </button>
              </p>
            </div>

            <div>
              <p className='text-[0.825rem] text-[#A2A2A2]'>
                Bank Name
              </p>
              <p className='flex items-center justify-between'>
                {
                  isGettingAccountDetails ?
                    <Skeleton className='w-[150px] h-5 rounded-xl' />
                    :
                    accountDetails.bank_name
                }
                <button onClick={() => copyToClipboard(accountDetails.bank_name)} disabled={isGettingAccountDetails}>
                  <TwoTonedCopyIcon />
                </button>
              </p>
            </div>

            <div>
              <p className='text-[0.825rem] text-[#A2A2A2]'>
                Account Number
              </p>
              <p className='flex items-center justify-between'>
                {
                  isGettingAccountDetails ?
                    <Skeleton className='w-[150px] h-5 rounded-xl' />
                    :
                    accountDetails.account_number
                }
                <button onClick={() => copyToClipboard(accountDetails.account_number)} disabled={isGettingAccountDetails}>
                  <TwoTonedCopyIcon />
                </button>
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8'>

          {/* <Button className='py-6 w-full text-[0.9rem]' onClick={confirmFunction} ref={buttonRef}>
            Print receipt
            {
              isConfirming &&
              <SmallSpinner color='black' />
            }
          </Button> */}

          <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8'>


            <Popover>
              <PopoverTrigger onClick={confirmFunction} className='flex-col-reverse sm:flex-row sm:justify-center sm:space-x-2 flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8'>
                {
                  isConfirming &&
                  <SmallSpinner color='black' />
                }
                I have received payment
              </PopoverTrigger>

              <PopoverContent align='end' className='flex flex-col max-w-max p-2 space-y-3 items-stretch'>
                <Button onClick={() => { printReceipt() }} className='py-6 w-full' ref={buttonRef}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" className='h-[20px] w-[20px]' fill="#000000" viewBox="0 0 256 256"><path d="M214.67,72H200V40a8,8,0,0,0-8-8H64a8,8,0,0,0-8,8V72H41.33C27.36,72,16,82.77,16,96v80a8,8,0,0,0,8,8H56v32a8,8,0,0,0,8,8H192a8,8,0,0,0,8-8V184h32a8,8,0,0,0,8-8V96C240,82.77,228.64,72,214.67,72ZM72,48H184V72H72ZM184,208H72V160H184Zm40-40H200V152a8,8,0,0,0-8-8H64a8,8,0,0,0-8,8v16H32V96c0-4.41,4.19-8,9.33-8H214.67c5.14,0,9.33,3.59,9.33,8Zm-24-52a12,12,0,1,1-12-12A12,12,0,0,1,200,116Z"></path></svg>
                  <span className='ml-1'> Print Receipt</span>
                  {
                    isConfirming &&
                    <SmallSpinner color='black' />
                  }
                </Button>
                <Button className='py-6 w-full' onClick={handlePaymentConfirmation} ref={buttonRef}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" className='h-[20px] w-[20px]' fill="#000000" viewBox="0 0 256 256"><path d="M196.8,169.6,141.33,128,196.8,86.4a8,8,0,0,0,0-12.8l-64-48A8,8,0,0,0,120,32v80L68.8,73.6a8,8,0,0,0-9.6,12.8L114.67,128,59.2,169.6a8,8,0,1,0,9.6,12.8L120,144v80a8,8,0,0,0,12.8,6.4l64-48a8,8,0,0,0,0-12.8ZM136,48l42.67,32L136,112Zm0,160V144l42.67,32Z"></path></svg>
                  <span className='ml-1'> Print with Bluetooth</span>

                  {isConfirming && <SmallSpinner color='black' />}
                </Button>

                {
                  /* {
                          stage.is_assessment && (
                              <button onClick={openConfirmSendAssessmentModal} className='p-2 hover:bg-primary-light-hover text-left text-[0.7rem] font-normal rounded-md text-header-text'>
                                  Send Interview Invite
                              </button>
                          )
                      } */
                }
              </PopoverContent>
            </Popover>
          </DialogFooter>
        </DialogFooter>
      </DialogContent>

      <CheckBrowserCompatibility
        isModalOpen={isBrowserCompartibilityModalOpen}
        closeModal={closeBrowserCompartibilityModal}
        browserVersion={chromeVersion}
        isOldVersion={isOldVersion}
      />
    </Dialog >
  )
}

export default ConfirmPaymentTransferModal