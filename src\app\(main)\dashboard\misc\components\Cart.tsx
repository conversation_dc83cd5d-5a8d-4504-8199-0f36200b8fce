import { CloseSquare } from "@/components/icons";
import { useCartStore } from "@/stores";
import { Input } from "@/components/ui";
import { convertNumberToNaira } from "@/utils/currency";

import React, { useEffect, useState, useRef } from "react";

import toast from "react-hot-toast";

import { TCartItem } from "../types";
import { Bag } from "iconsax-react";
import { cn } from "@/utils/classNames";

interface CartProps {
    items: TCartItem[];
    available_stock: TCartItem[];
    onUpdateQuantity: (id: string, change: number) => void;
    onRemoveItem: (id: string) => void;
}

export const Cart = React.forwardRef<HTMLDivElement, CartProps>((props, ref) => {
    const { items, onUpdateQuantity, onRemoveItem, available_stock } = props;
    const [focusedIndex, setFocusedIndex] = useState(0);
    const { add_item, remove_item, delete_item, update_item_quantity } = useCartStore()
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    const handleKeyDown = (event: React.KeyboardEvent<HTMLElement>) => {
        if (event.key === 'ArrowDown') {
            event.preventDefault();
            setFocusedIndex(prev => Math.min(prev + 1, items.length - 1));
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            setFocusedIndex(prev => Math.max(prev - 1, 0));
        } else if (event.key === 'Enter' && focusedIndex >= 0) {
            event.preventDefault();
            add_item(items[focusedIndex])
        } else if (event.key === '+' && focusedIndex >= 0) {
            event.preventDefault();
            onUpdateQuantity(items[focusedIndex]?.item_id, 1)
        } else if (event.key === '-' && focusedIndex >= 0) {
            event.preventDefault();
            remove_item(items[focusedIndex]?.item_id)
            if (focusedIndex === items.length) {
                setFocusedIndex(focusedIndex - 1)
            }
        } else if (event.key === 'Delete') {
            event.preventDefault();
            delete_item(items[focusedIndex]?.item_id)
            if (focusedIndex === items.length - 1) {
                setFocusedIndex(focusedIndex - 1)
            }
        } else if (event.key === 'Escape') {
            event.preventDefault();
            setFocusedIndex(-1)
        }
    };

    useEffect(() => {
        if (items.length === 0) {
            setFocusedIndex(-1)
        }
    }, [items])

    useEffect(() => {
        if (focusedIndex >= 0 && focusedIndex < items.length) {
            inputRefs.current[focusedIndex]?.focus();
        }
    }, [focusedIndex, items.length]);

    const handleInputChange = (item_id: string, value: string) => {
        const item = available_stock?.find(item => item.item_id === item_id)!;
        const quantity = parseInt(value, 10);
        if (!isNaN(quantity)) {
            update_item_quantity(item_id, quantity);
        }
        if (parseInt(value) > item.quantity) {
            toast.error(`inputed quantity exceeds available stock for ${item.item}, Available stock is ${item.quantity}`, { duration: 4000 })
            update_item_quantity(item_id, item.quantity);
        }
    };

    const handleInputFocus = (event: React.FocusEvent<HTMLInputElement>) => {
        event.target.select();
    };

    return (
        <div className="grow min-h-max shrink-0 bg-[#1C1C1C] divide-y-[1.2px] divide-[#262729] p-4 rounded-xl border-2 border-transparent focus:border-blue-600 !outline-none overflow-y-scroll"
            ref={ref}
            onKeyDown={handleKeyDown}
            tabIndex={0}
        >
            {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-[#d8d8dfd1] text-sm">
                    <Bag className="w-12 h-12 rounded-full mb-4" />
                    Checkout list will appear here
                </div>
            ) : (
                <>
                    {items.map((item, index) => (
                        <article
                            key={item.item_id}
                            tabIndex={index === focusedIndex ? 0 : -1}
                            className={cn("grid grid-cols-[1fr,0.5fr,1fr] items-center justify-between gap-5 py-3 px-2.5 text-sm",
                                {
                                    'bg-[#333]': index === focusedIndex
                                })
                            }
                        >
                            <p className="truncate">
                                {item.item}
                            </p>

                            <div className="flex items-center gap-1.5 justify-self-center">
                                <button
                                    onClick={() => remove_item(item.item_id)}
                                    aria-label={`Decrease quantity of ${item.item}`}
                                    className="flex items-center justify-center w-6 h-5 p-0 bg-[#F9F9F933] font-light text-sm rounded-md"
                                >
                                    -
                                </button>

                                <input
                                    ref={el => { inputRefs.current[index] = el; }}
                                    className="outline-none !h-8 text-center appearance-none w-full max-w-[50px] rounded-md"
                                    value={item.quantity}
                                    onChange={(e) => handleInputChange(item.item_id, e.target.value)}
                                    onFocus={handleInputFocus}
                                    type="number"
                                    min="1"
                                    max={item.quantity}
                                />

                                <button
                                    onClick={() => add_item(item)}
                                    aria-label={`Increase quantity of ${item.item}`}
                                    className="flex items-center justify-center w-6 h-5 p-0 bg-[#F9F9F933] font-light text-sm rounded-md"
                                >
                                    +
                                </button>
                            </div>

                            <div className="flex items-center gap-2 justify-self-end">
                                <p className="">
                                    {convertNumberToNaira(Number(item.selling_price) * item.quantity)}
                                </p>

                                <button onClick={() => onRemoveItem(item.item_id)} aria-label={`Remove ${item.item} from cart`}>
                                    <CloseSquare />
                                </button>
                            </div>
                        </article>
                    ))}
                </>
            )}
        </div>
    );
});

Cart.displayName = 'Cart';