import { managementAxios } from "@/lib/axios"
import { useQuery } from "@tanstack/react-query"

interface Props {
    pipeline_id: string
    stage_id: string
    enabled: boolean
}

const exportStageData = async (DTO: Props): Promise<Blob> => {
    const res = await managementAxios.get(`/orders/export-pipeline?pipeline_id=${DTO.pipeline_id}&stage_id=${DTO.stage_id}`, {
        responseType: 'blob'
    });
    return res.data;
}

export const UseExportStageData = (DTO: Props) => {
    return useQuery(
        {
            queryKey: ['export-stage-data', DTO.stage_id],
            queryFn: () => exportStageData(DTO),
            enabled: DTO.enabled
        }
    )
}
